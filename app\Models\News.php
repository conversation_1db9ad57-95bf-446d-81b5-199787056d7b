<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class News extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'excerpt',
        'image',
        'is_published',
        'is_featured',
        'sort_order',
        'published_at',
        'created_by',
    ];

    protected $casts = [
        'is_published' => 'boolean',
        'is_featured' => 'boolean',
        'published_at' => 'datetime',
    ];

    // Relationships
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('published_at', 'desc');
    }

    // Accessors
    protected function formattedPublishedAt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $this->published_at ? $this->published_at->format('d/m/Y H:i') : '-',
        );
    }

    protected function shortContent(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $this->excerpt ?: \Str::limit(strip_tags($this->content), 150),
        );
    }
}
