@extends('layouts.admin')

@section('title', 'รายละเอียดเมนูอาหาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ route('admin.menu-items.index') }}" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่รายการเมนู
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary fade-in-up">
                        <i class="fas fa-utensils me-2"></i>รายละเอียดเมนูอาหาร
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.menu-items.index') }}">เมนูอาหาร</a>
                            </li>
                            <li class="breadcrumb-item active">{{ $menuItem->name }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.menu-items.edit', $menuItem) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>แก้ไข
                    </a>
                    <a href="{{ route('admin.menu-items.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลเมนูอาหาร
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">หมวดหมู่</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-primary">{{ $menuItem->category->name }}</span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">ชื่อเมนู</label>
                                <p class="form-control-plaintext">{{ $menuItem->name }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label fw-bold">รายละเอียด</label>
                        <p class="form-control-plaintext">{{ $menuItem->description ?: 'ไม่มีรายละเอียด' }}</p>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">ราคา</label>
                                <p class="form-control-plaintext">
                                    @if($menuItem->price)
                                        <span class="text-success fw-bold fs-5">{{ number_format($menuItem->price, 0) }} บาท</span>
                                    @else
                                        <span class="text-muted">ไม่ระบุราคา</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">ลำดับการแสดง</label>
                                <p class="form-control-plaintext">{{ $menuItem->sort_order ?: 'ไม่ระบุ' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">สถานะ</label>
                                <p class="form-control-plaintext">
                                    @if($menuItem->is_featured)
                                        <span class="badge bg-warning text-dark me-2">
                                            <i class="fas fa-star"></i> เมนูแนะนำ
                                        </span>
                                    @endif
                                    @if($menuItem->is_active)
                                        <span class="badge bg-success">
                                            <i class="fas fa-eye"></i> แสดงในเว็บไซต์
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-eye-slash"></i> ซ่อนจากเว็บไซต์
                                        </span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">วันที่สร้าง</label>
                                <p class="form-control-plaintext">{{ $menuItem->created_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    </div>

                    @if($menuItem->updated_at != $menuItem->created_at)
                        <div class="mb-3">
                            <label class="form-label fw-bold">วันที่แก้ไขล่าสุด</label>
                            <p class="form-control-plaintext">{{ $menuItem->updated_at->format('d/m/Y H:i') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Image Card -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-image me-2"></i>รูปภาพ
                    </h6>
                </div>
                <div class="card-body text-center">
                    @if($menuItem->image)
                        <img src="{{ asset('storage/' . $menuItem->image) }}" alt="{{ $menuItem->name }}" 
                             class="img-fluid rounded shadow-sm mb-3" style="max-height: 300px;">
                        <p class="text-muted small">{{ $menuItem->name }}</p>
                    @else
                        <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" 
                             style="height: 200px;">
                            <div class="text-center">
                                <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                <p class="text-muted">ไม่มีรูปภาพ</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Actions Card -->
            <div class="card border-0 shadow-lg mt-4">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-cogs me-2"></i>การจัดการ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.menu-items.edit', $menuItem) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>แก้ไขข้อมูล
                        </a>
                        
                        @if($menuItem->is_active)
                            <form action="{{ route('admin.menu-items.update', $menuItem) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="is_active" value="0">
                                <button type="submit" class="btn btn-secondary w-100" 
                                        onclick="return confirm('ต้องการซ่อนเมนูนี้จากเว็บไซต์?')">
                                    <i class="fas fa-eye-slash me-2"></i>ซ่อนจากเว็บไซต์
                                </button>
                            </form>
                        @else
                            <form action="{{ route('admin.menu-items.update', $menuItem) }}" method="POST" class="d-inline">
                                @csrf
                                @method('PUT')
                                <input type="hidden" name="is_active" value="1">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="fas fa-eye me-2"></i>แสดงในเว็บไซต์
                                </button>
                            </form>
                        @endif

                        <hr>

                        <form action="{{ route('admin.menu-items.destroy', $menuItem) }}" method="POST" 
                              onsubmit="return confirm('ต้องการลบเมนูนี้? การกระทำนี้ไม่สามารถยกเลิกได้')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger w-100">
                                <i class="fas fa-trash me-2"></i>ลบเมนู
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Back Button Styles */
.btn-back {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.btn-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-back:hover::before {
    left: 100%;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B4513;
    color: #8B4513;
    background: #f8f9fa;
}

.btn-back i {
    transition: transform 0.3s ease;
}

.btn-back:hover i {
    transform: translateX(-3px);
}
</style>
@endpush
