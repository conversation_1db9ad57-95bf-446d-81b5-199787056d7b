<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class ImageController extends Controller
{
    /**
     * Get images from storage for the image picker
     */
    public function api(Request $request)
    {
        $category = $request->get('category', 'general');
        $search = $request->get('search', '');
        
        $images = [];
        
        // Define category folders
        $categoryFolders = [
            'general' => ['menu-items', 'categories', 'restaurant'],
            'menu' => ['menu-items'],
            'category' => ['categories'],
            'restaurant' => ['restaurant'],
            'about' => ['about-page'],
            'contact' => ['contact-page'],
            'news' => ['news']
        ];
        
        // Get folders to search based on category
        $foldersToSearch = $categoryFolders[$category] ?? $categoryFolders['general'];
        
        foreach ($foldersToSearch as $folder) {
            $folderPath = "public/{$folder}";
            
            if (Storage::exists($folderPath)) {
                $files = Storage::files($folderPath);
                
                foreach ($files as $file) {
                    $filename = basename($file);
                    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
                    
                    // Check if it's an image file
                    if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                        // Apply search filter if provided
                        if ($search && stripos($filename, $search) === false) {
                            continue;
                        }
                        
                        $relativePath = str_replace('public/', '', $file);
                        $fullUrl = Storage::url($file);
                        $size = Storage::size($file);
                        
                        $images[] = [
                            'id' => md5($file), // Use file hash as ID
                            'title' => $filename,
                            'original_filename' => $filename,
                            'path' => $relativePath,
                            'full_url' => asset('storage/' . $relativePath),
                            'size' => $size,
                            'formatted_size' => $this->formatBytes($size),
                            'category' => $folder,
                            'created_at' => date('Y-m-d H:i:s', Storage::lastModified($file))
                        ];
                    }
                }
            }
        }
        
        // Sort by creation date (newest first)
        usort($images, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return response()->json([
            'success' => true,
            'data' => $images,
            'total' => count($images)
        ]);
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Upload new image
     */
    public function upload(Request $request)
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
            'category' => 'required|string'
        ]);
        
        $category = $request->get('category', 'general');
        
        // Map category to folder
        $folderMap = [
            'menu' => 'menu-items',
            'category' => 'categories',
            'restaurant' => 'restaurant',
            'about' => 'about-page',
            'contact' => 'contact-page',
            'news' => 'news',
            'general' => 'gallery'
        ];
        
        $folder = $folderMap[$category] ?? 'gallery';
        
        if ($request->hasFile('image')) {
            $path = $request->file('image')->store($folder, 'public');
            $relativePath = str_replace('public/', '', $path);
            
            return response()->json([
                'success' => true,
                'message' => 'อัปโหลดรูปภาพเรียบร้อยแล้ว',
                'data' => [
                    'path' => $relativePath,
                    'url' => asset('storage/' . $relativePath)
                ]
            ]);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'ไม่สามารถอัปโหลดรูปภาพได้'
        ], 400);
    }
}
