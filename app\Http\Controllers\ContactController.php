<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ContactPage;
use App\Models\RestaurantInfo;

class ContactController extends Controller
{
    public function index()
    {
        $contactPage = ContactPage::getInfo();
        $restaurantInfo = RestaurantInfo::getInfo();

        return view('contact.index', compact('contactPage', 'restaurantInfo'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
        ], [
            'name.required' => 'กรุณากรอกชื่อ',
            'email.required' => 'กรุณากรอกอีเมล',
            'email.email' => 'รูปแบบอีเมลไม่ถูกต้อง',
            'subject.required' => 'กรุณากรอกหัวข้อ',
            'message.required' => 'กรุณากรอกข้อความ',
        ]);

        // Store contact data (you can create a Contact model if needed)
        $contactData = $request->all();

        // For now, we'll just return success
        // In production, you might want to:
        // 1. Save to database
        // 2. Send email notification
        // 3. Send auto-reply email

        return redirect()->back()->with('success', 'ส่งข้อความเรียบร้อยแล้ว เราจะติดต่อกลับโดยเร็วที่สุด');
    }
}
