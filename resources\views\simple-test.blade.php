<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Simple Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .info { background: #f0f0f0; padding: 20px; margin: 20px 0; }
        .form { background: #e8f4f8; padding: 20px; margin: 20px 0; }
        .success { background: #d4edda; padding: 10px; margin: 10px 0; color: #155724; }
        .error { background: #f8d7da; padding: 10px; margin: 10px 0; color: #721c24; }
    </style>
</head>
<body>
    <h1>Simple CSRF Test</h1>
    
    <div class="info">
        <h3>Session Info:</h3>
        <p><strong>Session ID:</strong> {{ session()->getId() }}</p>
        <p><strong>CSRF Token:</strong> {{ csrf_token() }}</p>
        <p><strong>Session Driver:</strong> {{ config('session.driver') }}</p>
        <p><strong>App Key Set:</strong> {{ config('app.key') ? 'Yes' : 'No' }}</p>
        <p><strong>Current Time:</strong> {{ now() }}</p>
    </div>
    
    <div class="form">
        <h3>Test Form:</h3>
        <form method="POST" action="{{ route('simple.test.post') }}">
            @csrf
            <p>
                <label>Test Data:</label><br>
                <input type="text" name="test_data" value="Hello World" style="width: 300px; padding: 5px;">
            </p>
            <p>
                <button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer;">
                    Submit Test
                </button>
            </p>
        </form>
        
        @if(session('test_result'))
            <div class="success">
                <strong>Success:</strong> {{ session('test_result') }}
            </div>
        @endif
        
        @if($errors->any())
            <div class="error">
                <strong>Errors:</strong>
                <ul>
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
    </div>
    
    <div class="info">
        <h3>Quick Links:</h3>
        <p>
            <a href="{{ route('home') }}">Home</a> | 
            <a href="{{ route('register') }}">Register</a> | 
            <a href="{{ route('login') }}">Login</a>
            @auth
                @if(Auth::user()->isAdmin())
                    | <a href="{{ route('admin.dashboard') }}">Admin</a>
                @endif
            @endauth
        </p>
    </div>
</body>
</html>
