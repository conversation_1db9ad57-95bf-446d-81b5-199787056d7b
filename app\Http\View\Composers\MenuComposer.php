<?php

namespace App\Http\View\Composers;

use Illuminate\View\View;
use App\Models\Category;
use App\Models\MenuItem;

class MenuComposer
{
    /**
     * Bind data to the view.
     *
     * @param  \Illuminate\View\View  $view
     * @return void
     */
    public function compose(View $view)
    {
        try {
            // Get all active categories with their active menu items
            $categories = Category::where('is_active', true)
                ->with(['menuItems' => function ($query) {
                    $query->where('is_active', true)
                          ->orderBy('name')
                          ->take(8); // จำกัดจำนวนเมนูในแต่ละหมวดหมู่
                }])
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get();

            // Get featured menu items
            $featuredMenuItems = MenuItem::where('is_active', true)
                ->where('is_featured', true)
                ->with('category')
                ->orderBy('name')
                ->take(5)
                ->get();

            \Log::info('MenuComposer data', [
                'categories_count' => $categories->count(),
                'featured_count' => $featuredMenuItems->count()
            ]);

            $view->with([
                'menuCategories' => $categories,
                'featuredMenuItems' => $featuredMenuItems
            ]);
        } catch (\Exception $e) {
            \Log::error('MenuComposer error: ' . $e->getMessage());
            $view->with([
                'menuCategories' => collect(),
                'featuredMenuItems' => collect()
            ]);
        }
    }
}
