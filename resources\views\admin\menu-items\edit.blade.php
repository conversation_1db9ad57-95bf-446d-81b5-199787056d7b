@extends('layouts.app')

@section('title', 'แก้ไขเมนูอาหาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ route('admin.menu-items.index') }}" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่รายการเมนู
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary fade-in-up">
                        <i class="fas fa-edit me-2"></i>แก้ไขเมนูอาหาร
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.menu-items.index') }}">เมนูอาหาร</a>
                            </li>
                            <li class="breadcrumb-item active">แก้ไข</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-edit me-2"></i>ข้อมูลเมนูอาหาร
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('admin.menu-items.update', $menuItem) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                    <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                                        <option value="">เลือกหมวดหมู่</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" {{ old('category_id', $menuItem->category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อเมนู <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name', $menuItem->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">รายละเอียด</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $menuItem->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">ราคา (บาท)</label>
                                    <input type="number" class="form-control @error('price') is-invalid @enderror" 
                                           id="price" name="price" value="{{ old('price', $menuItem->price) }}" min="0" step="0.01">
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                                    <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" name="sort_order" value="{{ old('sort_order', $menuItem->sort_order) }}" min="0">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">รูปภาพ</label>
                            @if($menuItem->image)
                                <div class="mb-2">
                                    <img src="{{ asset('storage/' . $menuItem->image) }}" alt="{{ $menuItem->name }}" 
                                         class="img-thumbnail" style="max-width: 200px;">
                                    <small class="text-muted d-block">รูปภาพปัจจุบัน</small>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            <small class="text-muted">อัปโหลดรูปภาพใหม่หากต้องการเปลี่ยน (JPG, PNG, GIF ขนาดไม่เกิน 2MB)</small>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" 
                                           {{ old('is_featured', $menuItem->is_featured) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_featured">
                                        <i class="fas fa-star text-warning me-1"></i>เมนูแนะนำ
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" 
                                           {{ old('is_active', $menuItem->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        <i class="fas fa-eye text-success me-1"></i>แสดงในเว็บไซต์
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.menu-items.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>ยกเลิก
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-eye me-2"></i>ตัวอย่าง
                    </h6>
                </div>
                <div class="card-body">
                    <div class="menu-preview">
                        @if($menuItem->image)
                            <img src="{{ asset('storage/' . $menuItem->image) }}" alt="{{ $menuItem->name }}" 
                                 class="img-fluid rounded mb-3" id="preview-image">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" 
                                 style="height: 200px;" id="preview-placeholder">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                        <h6 class="fw-bold" id="preview-name">{{ $menuItem->name }}</h6>
                        <p class="text-muted small mb-2" id="preview-description">{{ $menuItem->description ?: 'ไม่มีรายละเอียด' }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-primary fw-bold" id="preview-price">
                                {{ $menuItem->price ? number_format($menuItem->price, 0) . ' บาท' : 'ไม่ระบุราคา' }}
                            </span>
                            <div>
                                @if($menuItem->is_featured)
                                    <span class="badge bg-warning text-dark" id="preview-featured">
                                        <i class="fas fa-star"></i> แนะนำ
                                    </span>
                                @endif
                                @if($menuItem->is_active)
                                    <span class="badge bg-success" id="preview-active">
                                        <i class="fas fa-eye"></i> แสดง
                                    </span>
                                @else
                                    <span class="badge bg-secondary" id="preview-inactive">
                                        <i class="fas fa-eye-slash"></i> ซ่อน
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Live preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const priceInput = document.getElementById('price');
    const imageInput = document.getElementById('image');
    const featuredInput = document.getElementById('is_featured');
    const activeInput = document.getElementById('is_active');

    // Update preview on input change
    nameInput?.addEventListener('input', function() {
        document.getElementById('preview-name').textContent = this.value || 'ชื่อเมนู';
    });

    descriptionInput?.addEventListener('input', function() {
        document.getElementById('preview-description').textContent = this.value || 'ไม่มีรายละเอียด';
    });

    priceInput?.addEventListener('input', function() {
        const price = this.value ? parseFloat(this.value).toLocaleString() + ' บาท' : 'ไม่ระบุราคา';
        document.getElementById('preview-price').textContent = price;
    });

    // Image preview
    imageInput?.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewImage = document.getElementById('preview-image');
                const previewPlaceholder = document.getElementById('preview-placeholder');
                
                if (previewImage) {
                    previewImage.src = e.target.result;
                } else if (previewPlaceholder) {
                    previewPlaceholder.innerHTML = `<img src="${e.target.result}" class="img-fluid rounded" alt="Preview">`;
                }
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
/* Back Button Styles */
.btn-back {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.btn-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-back:hover::before {
    left: 100%;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B4513;
    color: #8B4513;
    background: #f8f9fa;
}

.btn-back i {
    transition: transform 0.3s ease;
}

.btn-back:hover i {
    transform: translateX(-3px);
}
@endpush
@endsection
