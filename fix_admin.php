<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

echo "=== Fixing Admin User ===\n";

// Delete existing admin user if exists
$existingAdmin = User::where('email', '<EMAIL>')->first();
if ($existingAdmin) {
    echo "Deleting existing admin user...\n";
    $existingAdmin->delete();
}

// Create new admin user
echo "Creating new admin user...\n";
$admin = User::create([
    'name' => 'Admin',
    'email' => '<EMAIL>',
    'password' => Hash::make('admin123'),
    'role' => 'admin'
]);

echo "✓ Admin user created successfully!\n";
echo "  ID: {$admin->id}\n";
echo "  Name: {$admin->name}\n";
echo "  Email: {$admin->email}\n";
echo "  Role: {$admin->role}\n";

// Test password
$passwordTest = Hash::check('admin123', $admin->password);
echo "  Password test: " . ($passwordTest ? 'PASS' : 'FAIL') . "\n";

// Test isAdmin method
echo "  isAdmin() test: " . ($admin->isAdmin() ? 'PASS' : 'FAIL') . "\n";

echo "\n=== Admin user is ready! ===\n";
echo "You can now login with:\n";
echo "Email: <EMAIL>\n";
echo "Password: admin123\n";
echo "URL: http://127.0.0.1:8000/login\n";
