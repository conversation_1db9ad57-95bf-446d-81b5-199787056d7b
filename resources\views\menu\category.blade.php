@extends('layouts.app')

@section('title', $category->name . ' - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-5">
    <!-- Header Section -->
    <div class="text-center mb-5">
        <div class="d-inline-block position-relative">
            <h1 class="display-4 fw-bold text-primary mb-3"></h1>
            <div class="position-absolute bottom-0 start-50 translate-middle-x" style="width: 100px; height: 4px; background: linear-gradient(90deg, var(--primary-color), var(--accent-color)); border-radius: 2px;"></div>
        </div>
        @if($category->description)
            <p class="lead text-muted mt-4">{{ $category->description }}</p>
        @endif
    </div>

    <!-- Menu Items Grid -->
    @if($menuItems->count() > 0)
        @if($category->slug === 'noodles')
            {{-- แสดงก๋วยเตี๋ยวแยกตามประเภท --}}
            @php
                $beefNoodles = $menuItems->filter(function($item) {
                    return $item->category->slug === 'noodle-beef';
                });
                $porkNoodles = $menuItems->filter(function($item) {
                    return $item->category->slug === 'noodle-pork';
                });
            @endphp

            @if($beefNoodles->count() > 0)
                <div class="mb-5">
                    <h3 class="text-danger mb-4">
                        <i class="fas fa-drumstick-bite me-2"></i>ก๋วยเตี๋ยวเนื้อ
                    </h3>
                    <div class="row g-4">
                        @foreach($beefNoodles as $item)
                            @include('menu.partials.menu-card', ['item' => $item])
                        @endforeach
                    </div>
                </div>
            @endif

            @if($porkNoodles->count() > 0)
                <div class="mb-5">
                    <h3 class="text-danger mb-4">
                        <i class="fas fa-bacon me-2"></i>ก๋วยเตี๋ยวหมู
                    </h3>
                    <div class="row g-4">
                        @foreach($porkNoodles as $item)
                            @include('menu.partials.menu-card', ['item' => $item])
                        @endforeach
                    </div>
                </div>
            @endif
        @else
            {{-- แสดงเมนูปกติ --}}
            <div class="row g-4">
                @foreach($menuItems as $item)
                    @include('menu.partials.menu-card', ['item' => $item])
                @endforeach
            </div>
        @endif

        <!-- Back to Menu Button -->
        <div class="text-center mt-5">
            <a href="{{ route('menu.index') }}" class="btn btn-outline-primary btn-lg px-5 py-3">
                <i class="fas fa-arrow-left me-2"></i>กลับไปดูเมนูทั้งหมด
            </a>
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-5">
            <h3 class="text-muted mb-3">ยังไม่มีเมนูในหมวดหมู่นี้</h3>
            <p class="text-muted mb-4">กรุณาติดตามเมนูใหม่ๆ ที่จะมาเร็วๆ นี้</p>
            <a href="{{ route('menu.index') }}" class="btn btn-primary btn-lg px-5 py-3">
                <i class="fas fa-arrow-left me-2"></i>กลับไปดูเมนูทั้งหมด
            </a>
        </div>
    @endif
</div>

<style>
.menu-item-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.menu-item-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
}

.menu-item-card:hover .card-img-top {
    transform: scale(1.05);
}

.price {
    position: relative;
}

.price::before {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--success), var(--accent-color));
    border-radius: 1px;
}
</style>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content bg-transparent border-0">
            <div class="modal-header border-0 pb-0">
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                <img id="modalImage" src="" alt="" class="img-fluid rounded shadow-lg" style="max-height: 80vh;">
            </div>
        </div>
    </div>
</div>

@endsection

@push('styles')
<style>
#imageModal .modal-content {
    background: transparent !important;
}

#imageModal .btn-close-white {
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    opacity: 1;
}

#imageModal .btn-close-white:hover {
    background: rgba(255,255,255,1);
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle image modal
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');

    if (imageModal && modalImage) {
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const imageSrc = button.getAttribute('data-image-src');
            const imageAlt = button.getAttribute('data-image-alt');

            modalImage.src = imageSrc;
            modalImage.alt = imageAlt;
        });

        // Close modal when clicking on the image
        modalImage.addEventListener('click', function() {
            const modalInstance = bootstrap.Modal.getInstance(imageModal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modalInstance = bootstrap.Modal.getInstance(imageModal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    }
});
</script>
@endpush
