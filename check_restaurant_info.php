<?php

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\RestaurantInfo;

echo "=== ตรวจสอบข้อมูลร้าน ===\n";

// ตรวจสอบข้อมูลปัจจุบัน
$info = RestaurantInfo::first();

if ($info) {
    echo "ข้อมูลปัจจุบัน:\n";
    echo "- ชื่อร้าน: " . ($info->name ?? 'ไม่มี') . "\n";
    echo "- คำอธิบาย: " . ($info->description ?? 'ไม่มี') . "\n";
    echo "- สโลแกน: " . ($info->tagline ?? 'ไม่มี') . "\n";
    echo "- สถานะ: " . ($info->is_active ? 'เปิดใช้งาน' : 'ปิดใช้งาน') . "\n";
    echo "- ID: " . $info->id . "\n";
} else {
    echo "ไม่พบข้อมูลร้าน - สร้างข้อมูลใหม่\n";
    
    // สร้างข้อมูลใหม่
    $info = RestaurantInfo::create([
        'name' => 'ร้านก๋วยเตี๋ยวเรือเข้าท่า',
        'description' => 'ก๋วยเตี๋ยวเรือต้นตำรับ รสชาติดั้งเดิม ด้วยสูตรลับเฉพาะตัว เครื่องเทศครบครอง',
        'tagline' => 'ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย',
        'is_active' => true,
    ]);
    
    echo "สร้างข้อมูลใหม่เรียบร้อยแล้ว\n";
}

echo "\n=== ทดสอบ getInfo() method ===\n";
$getInfo = RestaurantInfo::getInfo();
echo "ชื่อร้านจาก getInfo(): " . ($getInfo->name ?? 'ไม่มี') . "\n";
echo "คำอธิบายจาก getInfo(): " . ($getInfo->description ?? 'ไม่มี') . "\n";

echo "\n=== เสร็จสิ้น ===\n";
