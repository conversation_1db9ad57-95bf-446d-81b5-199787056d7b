<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

echo "=== Debug Login System ===\n\n";

// Check database connection
try {
    $users = User::count();
    echo "✓ Database connection OK - Total users: {$users}\n";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Check admin user
$admin = User::where('email', '<EMAIL>')->first();
if ($admin) {
    echo "✓ Admin user exists:\n";
    echo "  - ID: {$admin->id}\n";
    echo "  - Name: {$admin->name}\n";
    echo "  - Email: {$admin->email}\n";
    echo "  - Role: {$admin->role}\n";
    echo "  - Created: {$admin->created_at}\n";
    
    // Test password
    $passwordTest = Hash::check('admin123', $admin->password);
    echo "  - Password test (admin123): " . ($passwordTest ? 'PASS' : 'FAIL') . "\n";
    
    // Test isAdmin method
    echo "  - isAdmin() method: " . ($admin->isAdmin() ? 'true' : 'false') . "\n";
    
} else {
    echo "✗ Admin user not found\n";
    echo "Creating admin user...\n";
    
    try {
        $admin = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin'
        ]);
        echo "✓ Admin user created successfully with ID: {$admin->id}\n";
    } catch (Exception $e) {
        echo "✗ Failed to create admin user: " . $e->getMessage() . "\n";
    }
}

echo "\n=== All Users in Database ===\n";
$allUsers = User::all();
foreach ($allUsers as $user) {
    echo "ID: {$user->id} | Name: {$user->name} | Email: {$user->email} | Role: {$user->role}\n";
}

echo "\n=== Testing Manual Login ===\n";
$credentials = [
    'email' => '<EMAIL>',
    'password' => 'admin123'
];

echo "Credentials to test:\n";
echo "Email: {$credentials['email']}\n";
echo "Password: {$credentials['password']}\n";

// Find user manually
$user = User::where('email', $credentials['email'])->first();
if ($user && Hash::check($credentials['password'], $user->password)) {
    echo "✓ Manual authentication successful\n";
    echo "✓ User can login with these credentials\n";
    echo "✓ User role: {$user->role}\n";
    echo "✓ Is admin: " . ($user->isAdmin() ? 'yes' : 'no') . "\n";
} else {
    echo "✗ Manual authentication failed\n";
    if (!$user) {
        echo "  - User not found\n";
    } else {
        echo "  - Password mismatch\n";
    }
}

echo "\n=== Login URL ===\n";
echo "Please try logging in at: http://127.0.0.1:8000/login\n";
echo "Use the credentials above.\n";

echo "\nDebug completed.\n";
