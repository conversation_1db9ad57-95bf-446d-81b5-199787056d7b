<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\News;

class NewsController extends Controller
{
    public function index()
    {
        $featuredNews = News::published()
            ->featured()
            ->ordered()
            ->take(3)
            ->get();

        $news = News::published()
            ->ordered()
            ->paginate(12);

        return view('news.index', compact('news', 'featuredNews'));
    }

    public function show(News $news)
    {
        // Check if news is published
        if (!$news->is_published) {
            abort(404);
        }

        $relatedNews = News::published()
            ->where('id', '!=', $news->id)
            ->ordered()
            ->take(4)
            ->get();

        return view('news.show', compact('news', 'relatedNews'));
    }
}
