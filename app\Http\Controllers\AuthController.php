<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AuthController extends Controller
{
    public function showLoginForm()
    {
        if (Auth::check()) {
            return redirect()->intended(route('home'));
        }
        return view('auth.login');
    }

    public function login(Request $request)
    {
        \Log::info('Login attempt started', ['email' => $request->email]);

        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            \Log::warning('Login validation failed', ['errors' => $validator->errors()]);
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $credentials = $request->only('email', 'password');
        $remember = $request->has('remember');

        \Log::info('Attempting authentication', ['email' => $credentials['email']]);

        if (Auth::attempt($credentials, $remember)) {
            \Log::info('Authentication successful', ['user_id' => Auth::id(), 'user_role' => Auth::user()->role]);

            $request->session()->regenerate();

            if (Auth::user()->isAdmin()) {
                \Log::info('Redirecting admin to dashboard');
                $response = redirect('/admin')->with('success', 'เข้าสู่ระบบสำเร็จ');
                \Log::info('Redirect response created', ['status' => $response->getStatusCode()]);
                return $response;
            }

            \Log::info('Redirecting user to home');
            return redirect('/')->with('success', 'เข้าสู่ระบบสำเร็จ');
        }

        \Log::warning('Authentication failed', ['email' => $credentials['email']]);
        return redirect()->back()
            ->withErrors(['email' => 'อีเมลหรือรหัสผ่านไม่ถูกต้อง'])
            ->withInput();
    }



    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }
}
