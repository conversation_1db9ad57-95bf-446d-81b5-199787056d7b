<?php

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "<h1>ล้าง Cache</h1>";

try {
    // Clear various caches
    if (function_exists('opcache_reset')) {
        opcache_reset();
        echo "<p>✓ ล้าง OPCache แล้ว</p>";
    }
    
    // Clear Laravel caches
    \Illuminate\Support\Facades\Artisan::call('cache:clear');
    echo "<p>✓ ล้าง Application Cache แล้ว</p>";
    
    \Illuminate\Support\Facades\Artisan::call('config:clear');
    echo "<p>✓ ล้าง Config Cache แล้ว</p>";
    
    \Illuminate\Support\Facades\Artisan::call('view:clear');
    echo "<p>✓ ล้าง View Cache แล้ว</p>";
    
    \Illuminate\Support\Facades\Artisan::call('route:clear');
    echo "<p>✓ ล้าง Route Cache แล้ว</p>";
    
    echo "<h2>เสร็จสิ้น</h2>";
    echo "<p>ระบบพร้อมใช้งานแล้ว</p>";
    echo "<p><a href='/admin/font-settings'>ไปยังหน้าจัดการฟอนต์</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>เกิดข้อผิดพลาด: " . $e->getMessage() . "</p>";
}
?>
