<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\MenuItem;

class MenuController extends Controller
{
    public function index(Request $request)
    {
        $categories = Category::with(['menuItems' => function($query) {
            $query->where('is_active', true)->orderBy('sort_order');
        }])->orderBy('name')->get();

        $featuredMenus = MenuItem::where('is_active', true)
            ->where('is_featured', true)
            ->with('category')
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        // Build query for menu items with search
        $query = MenuItem::where('is_active', true)->with('category');

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->where('name', 'LIKE', "%{$searchTerm}%")
                  ->orWhere('description', 'LIKE', "%{$searchTerm}%")
                  ->orWhereHas('category', function($categoryQuery) use ($searchTerm) {
                      $categoryQuery->where('name', 'LIKE', "%{$searchTerm}%");
                  });
            });
        }

        $menuItems = $query->orderBy('sort_order')->get();

        return view('menu.index', compact('categories', 'featuredMenus', 'menuItems'));
    }

    public function category($slug)
    {
        // Handle special categories
        if ($slug === 'recommended') {
            // เมนูแนะนำ
            $category = (object) [
                'name' => 'เมนูแนะนำ',
                'slug' => 'recommended',
                'description' => 'เมนูยอดนิยมที่ลูกค้าแนะนำ',
                'image' => null
            ];

            $menuItems = MenuItem::where('is_active', true)
                ->where('is_featured', true)
                ->with('category')
                ->orderBy('name')
                ->get();

        } elseif ($slug === 'noodles') {
            // ก๋วยเตี๋ยวรวม (เนื้อ + หมู)
            $category = (object) [
                'name' => 'ก๋วยเตี๋ยว',
                'slug' => 'noodles',
                'description' => 'ก๋วยเตี๋ยวทุกประเภท เนื้อและหมู',
                'image' => null
            ];

            $noodleCategories = Category::whereIn('slug', ['noodle-beef', 'noodle-pork'])->pluck('id');
            $menuItems = MenuItem::where('is_active', true)
                ->whereIn('category_id', $noodleCategories)
                ->with('category')
                ->orderBy('name')
                ->get();

        } elseif ($slug === 'noodle-other') {
            // ก๋วยเตี๋ยวอื่นๆ
            $category = Category::where('slug', $slug)->firstOrFail();

            $menuItems = MenuItem::where('is_active', true)
                ->where('category_id', $category->id)
                ->with('category')
                ->orderBy('name')
                ->get();

        } else {
            // หมวดหมู่ปกติ
            $category = Category::where('slug', $slug)->firstOrFail();

            $menuItems = MenuItem::where('is_active', true)
                ->where('category_id', $category->id)
                ->orderBy('name')
                ->get();
        }

        return view('menu-category', compact('category', 'menuItems'));
    }

    public function show($id)
    {
        $menuItem = MenuItem::where('is_active', true)
            ->with('category')
            ->findOrFail($id);

        $relatedItems = MenuItem::where('is_active', true)
            ->where('category_id', $menuItem->category_id)
            ->where('id', '!=', $menuItem->id)
            ->take(4)
            ->get();

        return view('menu.show', compact('menuItem', 'relatedItems'));
    }
}
