<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('contact_pages', function (Blueprint $table) {
            $table->id();
            $table->string('title')->default('ติดต่อเรา');
            $table->text('description')->nullable(); // คำอธิบายหน้าติดต่อ
            $table->string('hero_image')->nullable(); // รูปหลักของหน้า

            // ข้อมูลติดต่อหลัก
            $table->string('address')->nullable(); // ที่อยู่
            $table->string('phone')->nullable(); // เบอร์โทรศัพท์
            $table->string('mobile')->nullable(); // เบอร์มือถือ
            $table->string('email')->nullable(); // อีเมล
            $table->string('line_id')->nullable(); // Line ID
            $table->string('facebook')->nullable(); // Facebook
            $table->string('instagram')->nullable(); // Instagram

            // เวลาทำการ
            $table->time('open_time')->nullable(); // เวลาเปิด
            $table->time('close_time')->nullable(); // เวลาปิด
            $table->json('open_days')->nullable(); // วันที่เปิด
            $table->text('special_hours')->nullable(); // เวลาพิเศษ/หมายเหตุ

            // แผนที่และตำแหน่ง
            $table->text('map_embed')->nullable(); // Google Maps embed code
            $table->decimal('latitude', 10, 8)->nullable(); // ละติจูด
            $table->decimal('longitude', 11, 8)->nullable(); // ลองจิจูด
            $table->text('directions')->nullable(); // คำแนะนำการเดินทาง

            // รูปภาพเพิ่มเติม
            $table->string('location_image')->nullable(); // รูปหน้าร้าน
            $table->string('interior_image')->nullable(); // รูปภายในร้าน
            $table->string('parking_image')->nullable(); // รูปที่จอดรถ

            // ข้อมูลเพิ่มเติม
            $table->text('parking_info')->nullable(); // ข้อมูลที่จอดรถ
            $table->text('public_transport')->nullable(); // การเดินทางโดยขนส่งสาธารณะ
            $table->text('additional_info')->nullable(); // ข้อมูลเพิ่มเติม

            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('contact_pages');
    }
};
