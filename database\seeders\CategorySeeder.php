<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = [
            [
                'name' => 'เมนูแนะนำ',
                'slug' => 'recommended',
                'description' => 'เมนูพิเศษที่ลูกค้าชื่นชอบมากที่สุด รสชาติเด็ด ต้องลอง',
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'name' => 'ของทานเล่น',
                'slug' => 'appetizers',
                'description' => 'อาหารเรียกน้ำย่อย ของทานเล่นหลากหลาย เหมาะสำหรับแชร์',
                'sort_order' => 2,
                'is_active' => true
            ],
            [
                'name' => 'เครื่องดื่ม',
                'slug' => 'beverages',
                'description' => 'เครื่องดื่มสดชื่น น้ำผลไม้ กาแฟ ชา และเครื่องดื่มเย็น',
                'sort_order' => 3,
                'is_active' => true
            ],
            [
                'name' => 'ก๋วยเตี๋ยวเนื้อ',
                'slug' => 'noodle-beef',
                'description' => 'ก๋วยเตี๋ยวเรือเนื้อ น้ำซุปเข้มข้น เนื้อนุ่ม รสชาติดั้งเดิม',
                'sort_order' => 4,
                'is_active' => true
            ],
            [
                'name' => 'ก๋วยเตี๋ยวหมู',
                'slug' => 'noodle-pork',
                'description' => 'ก๋วยเตี๋ยวเรือหมู หมูสับ หมูแดง น้ำซุปหอมหวน',
                'sort_order' => 5,
                'is_active' => true
            ]
        ];

        foreach ($categories as $category) {
            Category::create($category);
        }
    }
}
