<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Image;
use App\Models\AboutPage;
use Illuminate\Support\Facades\DB;

class AboutImageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create sample about page images
        $aboutImages = [
            [
                'title' => 'รูป Hero หน้าเกี่ยวกับเรา',
                'alt_text' => 'รูปหลักหน้าเกี่ยวกับเรา ร้านก๋วยเตี๋ยวเรือเข้าท่า',
                'description' => 'รูปภาพหลักที่แสดงในหน้าเกี่ยวกับเรา',
                'filename' => 'about-hero.jpg',
                'original_filename' => 'about-hero.jpg',
                'path' => 'gallery/about/about-hero.jpg',
                'url' => '/storage/gallery/about/about-hero.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 345760,
                'width' => 1200,
                'height' => 800,
                'category' => 'about',
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'title' => 'เรื่องราวของร้าน',
                'alt_text' => 'รูปประกอบเรื่องราวของร้านก๋วยเตี๋ยวเรือเข้าท่า',
                'description' => 'รูปภาพประกอบเรื่องราวและประวัติของร้าน',
                'filename' => 'about-story.jpg',
                'original_filename' => 'about-story.jpg',
                'path' => 'gallery/about/about-story.jpg',
                'url' => '/storage/gallery/about/about-story.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 289440,
                'width' => 1024,
                'height' => 768,
                'category' => 'about',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'title' => 'ทีมงานร้าน',
                'alt_text' => 'รูปทีมงานและบุคลากรร้านก๋วยเตี๋ยวเรือเข้าท่า',
                'description' => 'รูปภาพทีมงานและบุคลากรของร้าน',
                'filename' => 'about-team.jpg',
                'original_filename' => 'about-team.jpg',
                'path' => 'gallery/about/about-team.jpg',
                'url' => '/storage/gallery/about/about-team.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 256672,
                'width' => 800,
                'height' => 600,
                'category' => 'about',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'title' => 'บรรยากาศภายในร้าน',
                'alt_text' => 'บรรยากาศภายในร้านก๋วยเตี๋ยวเรือเข้าท่า',
                'description' => 'รูปภาพบรรยากาศภายในร้าน แกลเลอรี่ 1',
                'filename' => 'about-gallery-1.jpg',
                'original_filename' => 'about-gallery-1.jpg',
                'path' => 'gallery/about/about-gallery-1.jpg',
                'url' => '/storage/gallery/about/about-gallery-1.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 198432,
                'width' => 800,
                'height' => 600,
                'category' => 'about',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'title' => 'กระบวนการทำอาหาร',
                'alt_text' => 'กระบวนการทำก๋วยเตี๋ยวเรือ',
                'description' => 'รูปภาพกระบวนการทำอาหาร แกลเลอรี่ 2',
                'filename' => 'about-gallery-2.jpg',
                'original_filename' => 'about-gallery-2.jpg',
                'path' => 'gallery/about/about-gallery-2.jpg',
                'url' => '/storage/gallery/about/about-gallery-2.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 234567,
                'width' => 1024,
                'height' => 768,
                'category' => 'about',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'title' => 'วัตถุดิบคุณภาพ',
                'alt_text' => 'วัตถุดิบคุณภาพสำหรับทำก๋วยเตี๋ยวเรือ',
                'description' => 'รูปภาพวัตถุดิบคุณภาพ แกลเลอรี่ 3',
                'filename' => 'about-gallery-3.jpg',
                'original_filename' => 'about-gallery-3.jpg',
                'path' => 'gallery/about/about-gallery-3.jpg',
                'url' => '/storage/gallery/about/about-gallery-3.jpg',
                'mime_type' => 'image/jpeg',
                'size' => 212345,
                'width' => 800,
                'height' => 600,
                'category' => 'about',
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 6,
            ]
        ];

        foreach ($aboutImages as $imageData) {
            Image::create($imageData);
        }

        $this->command->info('About page images created successfully!');

        // Update about page to use these images
        $this->updateAboutPageImages();
    }

    private function updateAboutPageImages()
    {
        $aboutPage = AboutPage::getInfo();
        
        if ($aboutPage->exists) {
            $aboutPage->update([
                'hero_image' => 'gallery/about/about-hero.jpg',
                'story_image' => 'gallery/about/about-story.jpg',
                'team_image' => 'gallery/about/about-team.jpg',
                'gallery_image_1' => 'gallery/about/about-gallery-1.jpg',
                'gallery_image_2' => 'gallery/about/about-gallery-2.jpg',
                'gallery_image_3' => 'gallery/about/about-gallery-3.jpg',
            ]);
            
            $this->command->info("Updated About Page with sample images");
        } else {
            $this->command->info("About Page not found - please create it first");
        }
    }
}
