<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contact_pages', function (Blueprint $table) {
            $table->string('default_background')->nullable()->after('hero_image'); // รูปพื้นหลังเริ่มต้น
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('contact_pages', function (Blueprint $table) {
            $table->dropColumn('default_background');
        });
    }
};
