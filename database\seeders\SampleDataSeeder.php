<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\MenuItem;
use App\Models\News;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create Categories
        $categories = [
            [
                'name' => 'ก๋วยเตี๋ยวเรือ',
                'description' => 'ก๋วยเตี๋ยวเรือแท้ รสชาติดั้งเดิม',
                'is_active' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'ก๋วยเตี๋ยวน้ำใส',
                'description' => 'ก๋วยเตี๋ยวน้ำใสสไตล์บ้านๆ',
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'เครื่องดื่ม',
                'description' => 'เครื่องดื่มหลากหลาย',
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'ของทานเล่น',
                'description' => 'ของทานเล่นและของหวาน',
                'is_active' => true,
                'sort_order' => 4
            ]
        ];

        foreach ($categories as $categoryData) {
            Category::create($categoryData);
        }

        // Create Menu Items
        $menuItems = [
            [
                'category_id' => 1,
                'name' => 'ก๋วยเตี๋ยวเรือเนื้อ',
                'description' => 'ก๋วยเตี๋ยวเรือเนื้อแท้ๆ พร้อมเครื่องเคียงครบเครื่อง',
                'price' => 45,
                'is_active' => true,
                'is_featured' => true
            ],
            [
                'category_id' => 1,
                'name' => 'ก๋วยเตี๋ยวเรือหมู',
                'description' => 'ก๋วยเตี๋ยวเรือหมูสไตล์ดั้งเดิม',
                'price' => 40,
                'is_active' => true,
                'is_featured' => true
            ],
            [
                'category_id' => 1,
                'name' => 'ก๋วยเตี๋ยวเรือรวมมิตร',
                'description' => 'ก๋วยเตี๋ยวเรือรวมเนื้อและหมู',
                'price' => 50,
                'is_active' => true,
                'is_featured' => false
            ],
            [
                'category_id' => 2,
                'name' => 'ก๋วยเตี๋ยวน้ำใสเนื้อ',
                'description' => 'ก๋วยเตี๋ยวน้ำใสเนื้อนุ่ม',
                'price' => 40,
                'is_active' => true,
                'is_featured' => false
            ],
            [
                'category_id' => 3,
                'name' => 'น้ำเปล่า',
                'description' => 'น้ำเปล่าเซเว่น',
                'price' => 7,
                'is_active' => true,
                'is_featured' => false
            ],
            [
                'category_id' => 3,
                'name' => 'โค้ก',
                'description' => 'โค้กเย็นๆ',
                'price' => 15,
                'is_active' => true,
                'is_featured' => false
            ]
        ];

        foreach ($menuItems as $itemData) {
            MenuItem::create($itemData);
        }

        // Create News
        $newsItems = [
            [
                'title' => 'เปิดสาขาใหม่ที่ตลาดน้ำ',
                'content' => 'ร้านก๋วยเตี๋ยวเรือเข้าท่า เปิดสาขาใหม่ที่ตลาดน้ำดำเนินสะดวก มาลิ้มรสความอร่อยกันได้แล้ว',
                'is_published' => true,
                'is_featured' => true,
                'published_at' => now()
            ],
            [
                'title' => 'โปรโมชั่นพิเศษเดือนนี้',
                'content' => 'สั่งก๋วยเตี๋ยวเรือ 2 ชาม ลด 10 บาท ตลอดเดือนนี้',
                'is_published' => true,
                'is_featured' => true,
                'published_at' => now()
            ]
        ];

        foreach ($newsItems as $newsData) {
            News::create($newsData);
        }
    }
}
