@extends('layouts.app')

@section('title', $news->title . ' - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-decoration-none">หน้าหลัก</a></li>
            <li class="breadcrumb-item"><a href="{{ route('news.index') }}" class="text-decoration-none">ข่าวสาร</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $news->title }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- News Content -->
        <div class="col-lg-8">
            <article class="card border-0 shadow-lg">
                @if($news->image)
                    <img src="{{ asset('storage/' . $news->image) }}" 
                         class="card-img-top" 
                         alt="{{ $news->title }}"
                         style="height: 400px; object-fit: cover;">
                @endif
                
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h1 class="card-title h2 mb-0">{{ $news->title }}</h1>
                        @if($news->is_featured)
                            <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                                <i class="fas fa-star me-1"></i>ข่าวเด่น
                            </span>
                        @endif
                    </div>
                    
                    <div class="text-muted mb-4">
                        <i class="fas fa-calendar-alt me-2"></i>
                        {{ $news->created_at->format('d/m/Y H:i') }}
                        @if($news->created_at != $news->updated_at)
                            <span class="ms-3">
                                <i class="fas fa-edit me-2"></i>
                                แก้ไขล่าสุด: {{ $news->updated_at->format('d/m/Y H:i') }}
                            </span>
                        @endif
                    </div>
                    
                    @if($news->excerpt)
                        <div class="alert alert-info border-0 mb-4">
                            <h5 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>สรุป
                            </h5>
                            <p class="mb-0">{{ $news->excerpt }}</p>
                        </div>
                    @endif
                    
                    <div class="news-content">
                        {!! nl2br(e($news->content)) !!}
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="d-flex gap-3">
                        <a href="{{ route('news.index') }}" 
                           class="btn btn-primary">
                            <i class="fas fa-arrow-left me-2"></i>กลับไปดูข่าวสารทั้งหมด
                        </a>
                        <button class="btn btn-outline-primary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>พิมพ์
                        </button>
                        <button class="btn btn-outline-success" onclick="shareNews()">
                            <i class="fas fa-share-alt me-2"></i>แชร์
                        </button>
                    </div>
                </div>
            </article>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- News Info -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลข่าว
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>วันที่เผยแพร่:</strong><br>
                        <span class="text-muted">{{ $news->created_at->format('d/m/Y H:i') }}</span>
                    </div>
                    @if($news->created_at != $news->updated_at)
                        <div class="mb-3">
                            <strong>แก้ไขล่าสุด:</strong><br>
                            <span class="text-muted">{{ $news->updated_at->format('d/m/Y H:i') }}</span>
                        </div>
                    @endif
                    <div>
                        <strong>สถานะ:</strong><br>
                        <span class="badge bg-success">เผยแพร่แล้ว</span>
                        @if($news->is_featured)
                            <span class="badge bg-warning text-dark ms-1">ข่าวเด่น</span>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Related News -->
            @if($relatedNews->count() > 0)
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-newspaper me-2"></i>ข่าวสารอื่นๆ
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        @foreach($relatedNews as $item)
                            <div class="p-3 border-bottom">
                                @if($item->image)
                                    <img src="{{ asset('storage/' . $item->image) }}" 
                                         class="w-100 rounded mb-2" 
                                         alt="{{ $item->title }}"
                                         style="height: 120px; object-fit: cover;">
                                @endif
                                
                                <h6 class="mb-2">
                                    <a href="{{ route('news.show', $item->id) }}" 
                                       class="text-decoration-none text-dark">
                                        {{ $item->title }}
                                    </a>
                                </h6>
                                
                                @if($item->excerpt)
                                    <p class="text-muted small mb-2">{{ Str::limit($item->excerpt, 80) }}</p>
                                @endif
                                
                                <small class="text-muted">
                                    <i class="fas fa-calendar-alt me-1"></i>
                                    {{ $item->created_at->format('d/m/Y') }}
                                </small>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
function shareNews() {
    if (navigator.share) {
        navigator.share({
            title: '{{ $news->title }}',
            text: '{{ $news->excerpt ?? Str::limit($news->content, 100) }}',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('ลิงก์ถูกคัดลอกไปยังคลิปบอร์ดแล้ว');
        });
    }
}
</script>
@endpush
@endsection
