<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if admin user already exists
        $adminExists = User::where('email', '<EMAIL>')->exists();

        if (!$adminExists) {
            User::create([
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'admin'
            ]);
            echo "Admin user created successfully.\n";
        } else {
            echo "Admin user already exists.\n";
        }
    }
}
