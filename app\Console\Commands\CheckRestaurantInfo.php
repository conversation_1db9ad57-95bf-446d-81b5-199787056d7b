<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\RestaurantInfo;

class CheckRestaurantInfo extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'restaurant:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check restaurant info data';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('=== ตรวจสอบข้อมูลร้าน ===');
        
        $info = RestaurantInfo::first();
        
        if ($info) {
            $this->info('ข้อมูลปัจจุบัน:');
            $this->line('- ชื่อร้าน: ' . ($info->name ?? 'ไม่มี'));
            $this->line('- คำอธิบาย: ' . ($info->description ?? 'ไม่มี'));
            $this->line('- สโลแกน: ' . ($info->tagline ?? 'ไม่มี'));
            $this->line('- สถานะ: ' . ($info->is_active ? 'เปิดใช้งาน' : 'ปิดใช้งาน'));
            $this->line('- ID: ' . $info->id);
        } else {
            $this->error('ไม่พบข้อมูลร้าน');
        }
        
        $this->info('');
        $this->info('=== ทดสอบ getInfo() method ===');
        $getInfo = RestaurantInfo::getInfo();
        $this->line('ชื่อร้านจาก getInfo(): ' . ($getInfo->name ?? 'ไม่มี'));
        $this->line('คำอธิบายจาก getInfo(): ' . ($getInfo->description ?? 'ไม่มี'));
        
        return Command::SUCCESS;
    }
}
