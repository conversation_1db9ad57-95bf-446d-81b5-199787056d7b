<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Admin Links</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container py-5">
        <h1 class="text-center mb-5">ทดสอบ Admin Links</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Authentication Status</h5>
                    </div>
                    <div class="card-body">
                        @auth
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> 
                                เข้าสู่ระบบแล้ว: {{ auth()->user()->name }}
                                <br>
                                Role: {{ auth()->user()->role }}
                                <br>
                                Is Admin: {{ auth()->user()->isAdmin() ? 'Yes' : 'No' }}
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> 
                                ยังไม่ได้เข้าสู่ระบบ
                                <br>
                                <a href="{{ route('login') }}" class="btn btn-primary mt-2">เข้าสู่ระบบ</a>
                            </div>
                        @endauth
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Admin Routes Test</h5>
                    </div>
                    <div class="card-body">
                        @auth
                            @if(auth()->user()->isAdmin())
                                <div class="list-group">
                                    <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action">
                                        <i class="fas fa-tachometer-alt"></i> Admin Dashboard
                                    </a>
                                    <a href="{{ route('admin.categories.index') }}" class="list-group-item list-group-item-action">
                                        <i class="fas fa-tags"></i> Categories
                                    </a>
                                    <a href="{{ route('admin.menu-items.index') }}" class="list-group-item list-group-item-action">
                                        <i class="fas fa-utensils"></i> Menu Items
                                    </a>
                                    <a href="{{ route('admin.users.index') }}" class="list-group-item list-group-item-action">
                                        <i class="fas fa-users"></i> Users
                                    </a>
                                    <a href="{{ route('admin.news.index') }}" class="list-group-item list-group-item-action">
                                        <i class="fas fa-newspaper"></i> News
                                    </a>
                                </div>
                            @else
                                <div class="alert alert-danger">
                                    <i class="fas fa-ban"></i> คุณไม่มีสิทธิ์ Admin
                                </div>
                            @endif
                        @else
                            <div class="alert alert-info">
                                กรุณาเข้าสู่ระบบก่อน
                            </div>
                        @endauth
                    </div>
                </div>
            </div>
        </div>
        
        @auth
            @if(auth()->user()->isAdmin())
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Edit Links Test</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <h6>Categories Edit</h6>
                                        @for($i = 1; $i <= 3; $i++)
                                            <a href="{{ route('admin.categories.edit', $i) }}" class="btn btn-sm btn-outline-primary mb-1 d-block">
                                                Edit Category {{ $i }}
                                            </a>
                                        @endfor
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Menu Items Edit</h6>
                                        @for($i = 1; $i <= 3; $i++)
                                            <a href="{{ route('admin.menu-items.edit', $i) }}" class="btn btn-sm btn-outline-success mb-1 d-block">
                                                Edit Menu Item {{ $i }}
                                            </a>
                                        @endfor
                                    </div>
                                    <div class="col-md-4">
                                        <h6>Users Edit</h6>
                                        @for($i = 1; $i <= 3; $i++)
                                            <a href="{{ route('admin.users.edit', $i) }}" class="btn btn-sm btn-outline-warning mb-1 d-block">
                                                Edit User {{ $i }}
                                            </a>
                                        @endfor
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        @endauth
        
        <div class="text-center mt-4">
            <a href="{{ route('home') }}" class="btn btn-secondary">
                <i class="fas fa-home"></i> กลับหน้าหลัก
            </a>
            @auth
                <form method="POST" action="{{ route('logout') }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-sign-out-alt"></i> ออกจากระบบ
                    </button>
                </form>
            @endauth
        </div>
    </div>
</body>
</html>
