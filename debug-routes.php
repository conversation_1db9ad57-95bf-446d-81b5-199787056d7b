<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$request = Request::capture();
$response = $kernel->handle($request);

echo "=== ตรวจสอบ Admin Routes ===\n\n";

try {
    // Get all routes
    $routes = Route::getRoutes();
    
    echo "Admin Routes ที่พบ:\n";
    echo "==================\n";
    
    foreach ($routes as $route) {
        $name = $route->getName();
        $uri = $route->uri();
        $methods = implode('|', $route->methods());
        
        if ($name && strpos($name, 'admin.') === 0) {
            echo "Name: {$name}\n";
            echo "URI: {$uri}\n";
            echo "Methods: {$methods}\n";
            echo "---\n";
        }
    }
    
    echo "\nทดสอบ URL Generation:\n";
    echo "=====================\n";
    
    // Test specific routes
    $testRoutes = [
        'admin.dashboard',
        'admin.categories.index',
        'admin.categories.edit',
        'admin.menu-items.index',
        'admin.menu-items.edit',
        'admin.users.index',
        'admin.users.edit'
    ];
    
    foreach ($testRoutes as $routeName) {
        try {
            if ($routeName === 'admin.categories.edit' || $routeName === 'admin.menu-items.edit' || $routeName === 'admin.users.edit') {
                $url = route($routeName, 1); // Use ID 1 for edit routes
            } else {
                $url = route($routeName);
            }
            echo "✅ {$routeName}: {$url}\n";
        } catch (Exception $e) {
            echo "❌ {$routeName}: Error - " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
}

echo "\n=== สิ้นสุดการตรวจสอบ ===\n";
