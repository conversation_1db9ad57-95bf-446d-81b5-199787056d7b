<?php
// Debug script to check admin setup
echo "=== Admin Setup Debug ===\n\n";

// Check if we can connect to database
try {
    $pdo = new PDO('mysql:host=127.0.0.1;dbname=lastnoodle', 'root', '');
    echo "✓ Database connection: SUCCESS\n";
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        echo "✓ Admin user exists: SUCCESS\n";
        echo "  - ID: " . $admin['id'] . "\n";
        echo "  - Name: " . $admin['name'] . "\n";
        echo "  - Email: " . $admin['email'] . "\n";
        echo "  - Role: " . $admin['role'] . "\n";
        echo "  - Created: " . $admin['created_at'] . "\n";
        
        // Test password
        if (password_verify('admin123', $admin['password'])) {
            echo "✓ Password verification: SUCCESS\n";
        } else {
            echo "✗ Password verification: FAILED\n";
        }
    } else {
        echo "✗ Admin user exists: NOT FOUND\n";
    }
    
} catch (Exception $e) {
    echo "✗ Database connection: FAILED - " . $e->getMessage() . "\n";
}

echo "\n=== Login Instructions ===\n";
echo "1. Go to: http://127.0.0.1:8000/login\n";
echo "2. Use these credentials:\n";
echo "   Email: <EMAIL>\n";
echo "   Password: admin123\n";
echo "3. After login, you should be redirected to: http://127.0.0.1:8000/admin\n";
echo "\n=== Troubleshooting ===\n";
echo "If login fails, check:\n";
echo "- XAMPP MySQL is running\n";
echo "- Database 'lastnoodle' exists\n";
echo "- Laravel server is running (php artisan serve)\n";
echo "- Clear browser cache/cookies\n";
?>
