<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

$request = Request::capture();
$response = $kernel->handle($request);

echo "=== ทดสอบการเข้าถึง Admin ===\n\n";

try {
    // ตรวจสอบ admin user
    $admin = User::where('email', '<EMAIL>')->first();
    
    if ($admin) {
        echo "✅ พบ Admin User:\n";
        echo "   - ID: {$admin->id}\n";
        echo "   - Name: {$admin->name}\n";
        echo "   - Email: {$admin->email}\n";
        echo "   - Role: {$admin->role}\n";
        echo "   - Is Admin: " . ($admin->isAdmin() ? 'Yes' : 'No') . "\n\n";
        
        // ทดสอบ password
        if (Hash::check('admin123', $admin->password)) {
            echo "✅ Password ถูกต้อง\n\n";
        } else {
            echo "❌ Password ไม่ถูกต้อง\n\n";
        }
        
        // ทดสอบ login
        echo "=== ทดสอบ Login ===\n";
        if (Auth::attempt(['email' => '<EMAIL>', 'password' => 'admin123'])) {
            echo "✅ Login สำเร็จ\n";
            echo "   - Current User: " . Auth::user()->name . "\n";
            echo "   - Is Admin: " . (Auth::user()->isAdmin() ? 'Yes' : 'No') . "\n";
            
            // ทดสอบ admin routes
            echo "\n=== ทดสอบ Admin Routes ===\n";
            echo "Admin Dashboard URL: " . route('admin.dashboard') . "\n";
            echo "Admin Categories URL: " . route('admin.categories.index') . "\n";
            echo "Admin Menu Items URL: " . route('admin.menu-items.index') . "\n";
            echo "Admin Users URL: " . route('admin.users.index') . "\n";
            
        } else {
            echo "❌ Login ไม่สำเร็จ\n";
        }
        
    } else {
        echo "❌ ไม่พบ Admin User\n";
        echo "กำลังสร้าง Admin User ใหม่...\n";
        
        $newAdmin = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin'
        ]);
        
        echo "✅ สร้าง Admin User เรียบร้อยแล้ว\n";
        echo "   - Email: <EMAIL>\n";
        echo "   - Password: admin123\n";
    }
    
} catch (Exception $e) {
    echo "❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== สิ้นสุดการทดสอบ ===\n";
