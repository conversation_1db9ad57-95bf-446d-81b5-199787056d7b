<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>Debug Form - ตรวจสอบการส่งข้อมูล</h4>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success">{{ session('success') }}</div>
                        @endif
                        
                        @if(session('error'))
                            <div class="alert alert-danger">{{ session('error') }}</div>
                        @endif
                        
                        @if($errors->any())
                            <div class="alert alert-danger">
                                <h6>Validation Errors:</h6>
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        
                        @if(session('debug_data'))
                            <div class="alert alert-info">
                                <h6>ข้อมูลที่ได้รับ:</h6>
                                <pre>{{ print_r(session('debug_data'), true) }}</pre>
                            </div>
                        @endif
                        
                        <form method="POST" action="{{ route('debug.form.post') }}">
                            @csrf
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">ชื่อ</label>
                                <input type="text" class="form-control" id="name" name="name" value="ทดสอบ" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">อีเมล</label>
                                <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">รหัสผ่าน</label>
                                <input type="password" class="form-control" id="password" name="password" value="12345678" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password_confirmation" class="form-label">ยืนยันรหัสผ่าน</label>
                                <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" value="12345678" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">ส่งข้อมูล</button>
                        </form>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Session Info:</h6>
                                <ul>
                                    <li>Session ID: {{ session()->getId() }}</li>
                                    <li>CSRF Token: {{ csrf_token() }}</li>
                                    <li>Auth Check: {{ Auth::check() ? 'Yes' : 'No' }}</li>
                                    @if(Auth::check())
                                        <li>User: {{ Auth::user()->name }} ({{ Auth::user()->email }})</li>
                                    @endif
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Environment Info:</h6>
                                <ul>
                                    <li>App Environment: {{ app()->environment() }}</li>
                                    <li>App Debug: {{ config('app.debug') ? 'true' : 'false' }}</li>
                                    <li>Database: {{ config('database.default') }}</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <h6>ลิงก์ทดสอบ:</h6>
                            <a href="{{ route('test.register') }}" class="btn btn-outline-primary btn-sm me-2">ทดสอบ Register</a>
                            <a href="{{ route('test.login') }}" class="btn btn-outline-success btn-sm me-2">ทดสอบ Login</a>
                            <a href="{{ route('home') }}" class="btn btn-outline-secondary btn-sm">หน้าหลัก</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
