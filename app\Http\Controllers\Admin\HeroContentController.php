<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\RestaurantInfo;
use Illuminate\Http\Request;

class HeroContentController extends Controller
{
    /**
     * Display the hero content management page.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $restaurantInfo = RestaurantInfo::getInfo();
        return view('admin.hero-content.index', compact('restaurantInfo'));
    }

    /**
     * Show the form for editing hero content.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit()
    {
        $restaurantInfo = RestaurantInfo::getInfo();
        return view('admin.hero-content.edit', compact('restaurantInfo'));
    }

    /**
     * Update the hero content.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'tagline' => 'nullable|string',
        ]);

        $restaurantInfo = RestaurantInfo::getInfo();
        $data = $request->only(['name', 'description', 'tagline']);

        // If no existing record, create new one
        if (!$restaurantInfo->exists) {
            $data['is_active'] = true;
            RestaurantInfo::create($data);
        } else {
            $restaurantInfo->update($data);
        }

        return redirect()->route('admin.hero-content.index')
            ->with('success', 'ข้อความ Hero Section ถูกอัปเดตเรียบร้อยแล้ว');
    }
}
