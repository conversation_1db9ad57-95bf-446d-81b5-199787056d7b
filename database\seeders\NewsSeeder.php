<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\News;
use App\Models\User;

class NewsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $admin = User::where('role', 'admin')->first();

        $newsData = [
            [
                'title' => 'ยินดีต้อนรับสู่ร้านก๋วยเตี๋ยวเรือเข้าท่า',
                'excerpt' => 'ร้านก๋วยเตี๋ยวเรือแห่งใหม่ที่จะมาเสิร์ฟความอร่อยให้กับทุกท่าน',
                'content' => 'ร้านก๋วยเตี๋ยวเรือเข้าท่า เปิดให้บริการแล้ว! เรามีก๋วยเตี๋ยวเรือรสชาติต้นตำรับ ที่ปรุงด้วยน้ำซุปกระดูกหมูเคี่ยวนาน 8 ชั่วโมง พร้อมเครื่องเคียงครบครัน เช่น เลือดหมู ตับหมู หมูสับ และผักสดใหม่ ราคาเริ่มต้นเพียง 15 บาทต่อชาม',
                'is_published' => true,
                'is_featured' => true,
                'sort_order' => 1,
                'published_at' => now(),
                'created_by' => $admin->id ?? 1,
            ],
            [
                'title' => 'เมนูใหม่! ก๋วยเตี๋ยวเรือพิเศษ',
                'excerpt' => 'เพิ่มเมนูใหม่ ก๋วยเตี๋ยวเรือพิเศษ พร้อมเครื่องเคียงเพิ่มเติม',
                'content' => 'เรามีเมนูใหม่มาเสิร์ฟแล้ว! ก๋วยเตี๋ยวเรือพิเศษ ที่มาพร้อมกับเครื่องเคียงเพิ่มเติม ได้แก่ หมูแดง ไข่ต้ม และผักกาดขาว ในราคาเพียง 25 บาทต่อชาม รสชาติเข้มข้น หอมหวาน อร่อยจนต้องกลับมาอีก',
                'is_published' => true,
                'is_featured' => false,
                'sort_order' => 2,
                'published_at' => now()->subDays(3),
                'created_by' => $admin->id ?? 1,
            ],
            [
                'title' => 'โปรโมชั่นพิเศษ! ซื้อ 10 ชาม แถม 1 ชาม',
                'excerpt' => 'โปรโมชั่นสำหรับลูกค้าประจำ ซื้อครบ 10 ชาม รับฟรี 1 ชาม',
                'content' => 'สำหรับลูกค้าที่รักเรา เรามีโปรโมชั่นพิเศษ ซื้อก๋วยเตี๋ยวเรือครบ 10 ชาม รับฟรี 1 ชาม (ใช้ได้กับเมนูราคาเท่ากันหรือต่ำกว่า) โปรโมชั่นนี้ใช้ได้ตลอดเดือน ไม่มีวันหมดอายุ',
                'is_published' => true,
                'is_featured' => true,
                'sort_order' => 3,
                'published_at' => now()->subDays(7),
                'created_by' => $admin->id ?? 1,
            ],
            [
                'title' => 'เวลาเปิด-ปิดร้าน',
                'excerpt' => 'ข้อมูลเวลาเปิด-ปิดร้านและวันหยุด',
                'content' => 'ร้านเปิดให้บริการทุกวัน เวลา 06:00 - 14:00 น. และ 17:00 - 21:00 น. หยุดวันจันทร์ สำหรับการสั่งล่วงหน้า สามารถติดต่อได้ที่เบอร์โทร 02-xxx-xxxx',
                'is_published' => true,
                'is_featured' => false,
                'sort_order' => 4,
                'published_at' => now()->subDays(10),
                'created_by' => $admin->id ?? 1,
            ],
        ];

        foreach ($newsData as $news) {
            News::create($news);
        }
    }
}
