@extends('layouts.app')

@section('title', 'เพิ่มเมนูอาหารใหม่ - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ route('admin.menu-items.index') }}" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่รายการเมนู
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary fade-in-up">
                        <i class="fas fa-plus me-2"></i>เพิ่มเมนูอาหารใหม่
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.menu-items.index') }}">เมนูอาหาร</a>
                            </li>
                            <li class="breadcrumb-item active">เพิ่มใหม่</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-edit me-2"></i>ข้อมูลเมนูอาหาร
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('admin.menu-items.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">หมวดหมู่ <span class="text-danger">*</span></label>
                                    <select class="form-select @error('category_id') is-invalid @enderror" 
                                            id="category_id" 
                                            name="category_id" 
                                            required>
                                        <option value="">เลือกหมวดหมู่</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}" 
                                                    {{ old('category_id', $selectedCategory) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">ชื่อเมนู <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control @error('name') is-invalid @enderror" 
                                           id="name" 
                                           name="name" 
                                           value="{{ old('name') }}" 
                                           required
                                           placeholder="เช่น ก๋วยเตี๋ยวเรือเนื้อพิเศษ">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">คำอธิบาย</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" 
                                      name="description" 
                                      rows="3"
                                      placeholder="คำอธิบายเกี่ยวกับเมนูนี้">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">ราคา (บาท)</label>
                                    <div class="input-group">
                                        <input type="number" 
                                               class="form-control @error('price') is-invalid @enderror" 
                                               id="price" 
                                               name="price" 
                                               value="{{ old('price') }}" 
                                               min="0"
                                               step="0.01"
                                               placeholder="0.00">
                                        <span class="input-group-text">บาท</span>
                                    </div>
                                    <small class="form-text text-muted">หากไม่กรอก จะแสดงเป็น "ราคาตามสอบถาม"</small>
                                    @error('price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                                    <input type="number" 
                                           class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" 
                                           name="sort_order" 
                                           value="{{ old('sort_order', 0) }}" 
                                           min="0"
                                           placeholder="0">
                                    <small class="form-text text-muted">ตัวเลขน้อยจะแสดงก่อน</small>
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="image" class="form-label">รูปภาพเมนู</label>
                            <input type="file" 
                                   class="form-control @error('image') is-invalid @enderror" 
                                   id="image" 
                                   name="image" 
                                   accept="image/*">
                            <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_featured" 
                                               name="is_featured" 
                                               value="1" 
                                               {{ old('is_featured') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_featured">
                                            <i class="fas fa-star text-warning me-1"></i>เมนูแนะนำ
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active" 
                                               value="1" 
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            <i class="fas fa-eye text-success me-1"></i>เปิดใช้งาน
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2 mt-4">
                            <a href="{{ route('admin.menu-items.index') }}" class="btn btn-secondary px-4">
                                <i class="fas fa-times me-2"></i>ยกเลิก
                            </a>
                            <button type="submit" class="btn btn-primary px-4">
                                <i class="fas fa-save me-2"></i>บันทึก
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Preview -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-eye me-2"></i>ตัวอย่าง
                    </h6>
                </div>
                <div class="card-body">
                    <div id="preview-card" class="card border">
                        <div id="preview-image" class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 id="preview-name" class="card-title text-primary mb-0">ชื่อเมนู</h6>
                                <span id="preview-category" class="badge bg-secondary">หมวดหมู่</span>
                            </div>
                            <p id="preview-description" class="card-text text-muted small">คำอธิบายเมนู</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <strong id="preview-price" class="text-primary">ราคาตามสอบถาม</strong>
                                <div id="preview-badges"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const descriptionInput = document.getElementById('description');
    const priceInput = document.getElementById('price');
    const categorySelect = document.getElementById('category_id');
    const imageInput = document.getElementById('image');
    const isFeaturedInput = document.getElementById('is_featured');
    const isActiveInput = document.getElementById('is_active');
    
    function updatePreview() {
        // Update name
        document.getElementById('preview-name').textContent = nameInput.value || 'ชื่อเมนู';
        
        // Update description
        document.getElementById('preview-description').textContent = descriptionInput.value || 'คำอธิบายเมนู';
        
        // Update price
        const price = priceInput.value;
        document.getElementById('preview-price').textContent = price ? 
            parseFloat(price).toLocaleString() + ' บาท' : 'ราคาตามสอบถาม';
        
        // Update category
        const selectedOption = categorySelect.options[categorySelect.selectedIndex];
        document.getElementById('preview-category').textContent = selectedOption.text || 'หมวดหมู่';
        
        // Update badges
        const badges = [];
        if (isFeaturedInput.checked) {
            badges.push('<span class="badge bg-warning text-dark"><i class="fas fa-star me-1"></i>แนะนำ</span>');
        }
        if (!isActiveInput.checked) {
            badges.push('<span class="badge bg-danger">ปิดใช้งาน</span>');
        }
        document.getElementById('preview-badges').innerHTML = badges.join(' ');
    }
    
    // Image preview
    imageInput.addEventListener('change', function() {
        const file = this.files[0];
        const previewImage = document.getElementById('preview-image');
        
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.innerHTML = `<img src="${e.target.result}" class="card-img-top" style="height: 200px; object-fit: cover;">`;
            };
            reader.readAsDataURL(file);
        } else {
            previewImage.innerHTML = '<i class="fas fa-image fa-3x text-muted"></i>';
        }
    });
    
    // Add event listeners
    [nameInput, descriptionInput, priceInput, categorySelect, isFeaturedInput, isActiveInput].forEach(input => {
        input.addEventListener('input', updatePreview);
        input.addEventListener('change', updatePreview);
    });
    
    // Initial preview update
    updatePreview();
});
</script>
/* Back Button Styles */
.btn-back {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.btn-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-back:hover::before {
    left: 100%;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B4513;
    color: #8B4513;
    background: #f8f9fa;
}

.btn-back i {
    transition: transform 0.3s ease;
}

.btn-back:hover i {
    transform: translateX(-3px);
}
@endpush
@endsection
