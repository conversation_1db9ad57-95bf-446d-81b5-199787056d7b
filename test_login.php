<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

echo "=== Testing Login System ===\n";

// Check if admin user exists
$admin = User::where('email', '<EMAIL>')->first();
if ($admin) {
    echo "✓ Admin user found: {$admin->name} ({$admin->email})\n";
    echo "✓ Role: {$admin->role}\n";

    // Test password
    $passwordCheck = Hash::check('admin123', $admin->password);
    echo "✓ Password check (admin123): " . ($passwordCheck ? 'PASS' : 'FAIL') . "\n";

    // Test isAdmin method
    echo "✓ isAdmin(): " . ($admin->isAdmin() ? 'true' : 'false') . "\n";
} else {
    echo "✗ Admin user not found\n";

    // Create admin user
    echo "Creating admin user...\n";
    $admin = User::create([
        'name' => 'Admin',
        'email' => '<EMAIL>',
        'password' => Hash::make('admin123'),
        'role' => 'admin'
    ]);
    echo "✓ Admin user created: {$admin->id}\n";
}

// List all users
echo "\n=== All Users ===\n";
$users = User::all();
foreach ($users as $user) {
    echo "ID: {$user->id}, Name: {$user->name}, Email: {$user->email}, Role: {$user->role}\n";
}

echo "\n=== Testing Auth Attempt ===\n";
$credentials = ['email' => '<EMAIL>', 'password' => 'admin123'];

// Simulate auth attempt
if (Auth::attempt($credentials)) {
    echo "✓ Auth::attempt() successful\n";
    echo "✓ Authenticated user: " . Auth::user()->name . "\n";
    echo "✓ User role: " . Auth::user()->role . "\n";
    echo "✓ Is admin: " . (Auth::user()->isAdmin() ? 'true' : 'false') . "\n";
} else {
    echo "✗ Auth::attempt() failed\n";
}

echo "\nTest completed.\n";
