<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\MenuItem;
use App\Models\User;
use App\Models\News;

class AdminController extends Controller
{
    public function dashboard()
    {
        try {
            $stats = [
                'categories' => Category::count(),
                'menu_items' => MenuItem::count(),
                'active_menu_items' => MenuItem::where('is_active', true)->count(),
                'featured_menu_items' => MenuItem::where('is_featured', true)->count(),
                'news' => News::count(),
                'published_news' => News::where('is_published', true)->count(),
                'featured_news' => News::where('is_featured', true)->count(),
            ];

            $recentMenuItems = MenuItem::with('category')
                ->latest()
                ->take(5)
                ->get();

            return view('admin.dashboard', compact('stats', 'recentMenuItems'));
        } catch (\Exception $e) {
            \Log::error('Admin dashboard error: ' . $e->getMessage());

            // Fallback to simple dashboard
            $stats = [
                'categories' => 0,
                'menu_items' => 0,
                'news' => 0,
            ];

            return view('admin.simple-dashboard', compact('stats'));
        }
    }
}
