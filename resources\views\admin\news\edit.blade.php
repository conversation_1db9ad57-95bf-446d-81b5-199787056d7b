@extends('layouts.app')

@section('title', 'แก้ไขข่าวสาร - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary fade-in-up">
                        <i class="fas fa-edit me-2"></i>แก้ไขข่าวสาร
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.news.index') }}">ข่าวสาร</a>
                            </li>
                            <li class="breadcrumb-item active">แก้ไข</li>
                        </ol>
                    </nav>
                </div>
                <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>กลับ
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h5 class="mb-0 text-white">
                        <i class="fas fa-edit me-2"></i>ข้อมูลข่าวสาร
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('admin.news.update', $news) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">หัวข้อข่าว <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $news->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">เนื้อหา <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="8" required>{{ old('content', $news->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="excerpt" class="form-label">สรุปข่าว</label>
                            <textarea class="form-control @error('excerpt') is-invalid @enderror" 
                                      id="excerpt" name="excerpt" rows="3">{{ old('excerpt', $news->excerpt) }}</textarea>
                            <small class="text-muted">สรุปสั้นๆ ของข่าวสารที่จะแสดงในหน้ารายการ</small>
                            @error('excerpt')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="image" class="form-label">รูปภาพ</label>
                            @if($news->image)
                                <div class="mb-2">
                                    <img src="{{ asset('storage/' . $news->image) }}" alt="{{ $news->title }}" 
                                         class="img-thumbnail" style="max-width: 200px;">
                                    <small class="text-muted d-block">รูปภาพปัจจุบัน</small>
                                </div>
                            @endif
                            <input type="file" class="form-control @error('image') is-invalid @enderror" 
                                   id="image" name="image" accept="image/*">
                            <small class="text-muted">อัปโหลดรูปภาพใหม่หากต้องการเปลี่ยน (JPG, PNG, GIF ขนาดไม่เกิน 2MB)</small>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" value="1" 
                                           {{ old('is_featured', $news->is_featured) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_featured">
                                        <i class="fas fa-star text-warning me-1"></i>ข่าวเด่น
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="is_published" name="is_published" value="1" 
                                           {{ old('is_published', $news->is_published) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_published">
                                        <i class="fas fa-eye text-success me-1"></i>เผยแพร่
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>ยกเลิก
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-lg">
                <div class="card-header">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-eye me-2"></i>ตัวอย่าง
                    </h6>
                </div>
                <div class="card-body">
                    <div class="news-preview">
                        @if($news->image)
                            <img src="{{ asset('storage/' . $news->image) }}" alt="{{ $news->title }}" 
                                 class="img-fluid rounded mb-3" id="preview-image">
                        @else
                            <div class="bg-light rounded d-flex align-items-center justify-content-center mb-3" 
                                 style="height: 200px;" id="preview-placeholder">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                        <h6 class="fw-bold" id="preview-title">{{ $news->title }}</h6>
                        <p class="text-muted small mb-2" id="preview-excerpt">{{ $news->excerpt ?: 'ไม่มีสรุปข่าว' }}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">{{ $news->created_at->format('d/m/Y') }}</small>
                            <div>
                                @if($news->is_featured)
                                    <span class="badge bg-warning text-dark" id="preview-featured">
                                        <i class="fas fa-star"></i> เด่น
                                    </span>
                                @endif
                                @if($news->is_published)
                                    <span class="badge bg-success" id="preview-published">
                                        <i class="fas fa-eye"></i> เผยแพร่
                                    </span>
                                @else
                                    <span class="badge bg-secondary" id="preview-draft">
                                        <i class="fas fa-eye-slash"></i> ร่าง
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
// Live preview functionality
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('title');
    const excerptInput = document.getElementById('excerpt');
    const imageInput = document.getElementById('image');
    const featuredInput = document.getElementById('is_featured');
    const publishedInput = document.getElementById('is_published');

    // Update preview on input change
    titleInput?.addEventListener('input', function() {
        document.getElementById('preview-title').textContent = this.value || 'หัวข้อข่าว';
    });

    excerptInput?.addEventListener('input', function() {
        document.getElementById('preview-excerpt').textContent = this.value || 'ไม่มีสรุปข่าว';
    });

    // Image preview
    imageInput?.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewImage = document.getElementById('preview-image');
                const previewPlaceholder = document.getElementById('preview-placeholder');
                
                if (previewImage) {
                    previewImage.src = e.target.result;
                } else if (previewPlaceholder) {
                    previewPlaceholder.innerHTML = `<img src="${e.target.result}" class="img-fluid rounded" alt="Preview">`;
                }
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endpush
@endsection
