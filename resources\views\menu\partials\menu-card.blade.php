<div class="col-lg-4 col-md-6 mb-4">
    <div class="card h-100 border-0 shadow-lg menu-item-card">
        @if($item->image)
            <div class="position-relative overflow-hidden" style="height: 250px;">
                <img src="{{ asset('storage/' . $item->image) }}"
                     class="card-img-top h-100 w-100"
                     style="object-fit: cover; transition: transform 0.3s ease; cursor: pointer;"
                     alt="{{ $item->name }}"
                     data-bs-toggle="modal"
                     data-bs-target="#imageModal"
                     data-image-src="{{ asset('storage/' . $item->image) }}"
                     data-image-alt="{{ $item->name }}">
                <div class="position-absolute top-0 end-0 m-3">
                    @if($item->is_featured)
                        <span class="badge bg-warning text-dark px-3 py-2">
                            <i class="fas fa-star me-1"></i>แนะนำ
                        </span>
                    @endif
                </div>
                @if(isset($item->category))
                    <div class="position-absolute top-0 start-0 m-3">
                        <span class="badge bg-primary px-3 py-2">
                            {{ $item->category->name }}
                        </span>
                    </div>
                @endif
            </div>
        @else
            <div class="card-img-top d-flex align-items-center justify-content-center bg-light position-relative" style="height: 250px;">
                <img src="{{ asset('images/menu/placeholder.svg') }}"
                     alt="ไม่มีรูปภาพ"
                     class="opacity-75"
                     style="width: 120px; height: 120px; object-fit: contain;">
            </div>
        @endif

        <div class="card-body d-flex flex-column">
            <h5 class="card-title fw-bold text-primary mb-2 text-center">{{ $item->name }}</h5>
            
            @if($item->description)
                <p class="card-text text-muted flex-grow-1">{{ $item->description }}</p>
            @endif

            <div class="d-flex justify-content-between align-items-center mt-auto">
                <div class="price">
                    <span class="h4 fw-bold text-success mb-0">{{ number_format($item->price) }} ฿</span>
                </div>
                <a href="{{ route('menu.show', $item->id) }}" class="btn btn-primary btn-sm px-3">
                    <i class="fas fa-eye me-1"></i>ดูรายละเอียด
                </a>
            </div>
        </div>
    </div>
</div>
