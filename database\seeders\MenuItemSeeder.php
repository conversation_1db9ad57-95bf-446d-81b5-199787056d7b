<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MenuItem;
use App\Models\Category;

class MenuItemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $categories = Category::all();

        // เมนูแนะนำ
        $featuredCategory = $categories->where('slug', 'recommended')->first();
        if ($featuredCategory) {
            $featuredMenus = [
                [
                    'category_id' => $featuredCategory->id,
                    'name' => 'ก๋วยเตี๋ยวเรือเนื้อพิเศษ',
                    'description' => 'ก๋วยเตี๋ยวเรือเนื้อสูตรพิเศษ เนื้อนุ่ม น้ำซุปเข้มข้น เครื่องเทศครบเครื่อง',
                    'price' => 65.00,
                    'image' => 'images/menu/noodle-beef-special.svg',
                    'is_featured' => true,
                    'is_active' => true,
                    'sort_order' => 1
                ],
                [
                    'category_id' => $featuredCategory->id,
                    'name' => 'ก๋วยเตี๋ยวเรือหมูสามชั้น',
                    'description' => 'ก๋วยเตี๋ยวเรือหมูสามชั้นหั่นบาง น้ำซุปหอมหวน รสชาติเข้มข้น',
                    'price' => 60.00,
                    'image' => 'images/menu/noodle-pork-special.svg',
                    'is_featured' => true,
                    'is_active' => true,
                    'sort_order' => 2
                ],
                [
                    'category_id' => $featuredCategory->id,
                    'name' => 'ก๋วยเตี๋ยวเรือรวมมิตร',
                    'description' => 'ก๋วยเตี๋ยวเรือรวมเนื้อและหมู พร้อมเครื่องในครบครัน',
                    'price' => 70.00,
                    'image' => 'images/menu/mixed-noodle.svg',
                    'is_featured' => true,
                    'is_active' => true,
                    'sort_order' => 3
                ]
            ];

            foreach ($featuredMenus as $menu) {
                MenuItem::create($menu);
            }
        }

        // ของทานเล่น
        $appetizerCategory = $categories->where('slug', 'appetizers')->first();
        if ($appetizerCategory) {
            $appetizers = [
                [
                    'category_id' => $appetizerCategory->id,
                    'name' => 'เต้าหู้ทอด',
                    'description' => 'เต้าหู้ทอดกรอบนอกนุ่มใน เสิร์ฟพร้อมน้ำจิ้มรสเด็ด',
                    'price' => 25.00,
                    'image' => 'images/menu/tofu-fried.svg',
                    'is_featured' => false,
                    'is_active' => true,
                    'sort_order' => 1
                ],
                [
                    'category_id' => $appetizerCategory->id,
                    'name' => 'หมูสะเต๊ะ',
                    'description' => 'หมูสะเต๊ะย่างหอม เสิร์ฟพร้อมน้ำจิ้มถั่วและอาจาด',
                    'price' => 35.00,
                    'is_featured' => false,
                    'is_active' => true,
                    'sort_order' => 2
                ]
            ];

            foreach ($appetizers as $menu) {
                MenuItem::create($menu);
            }
        }

        // เครื่องดื่ม
        $beverageCategory = $categories->where('slug', 'beverages')->first();
        if ($beverageCategory) {
            $beverages = [
                [
                    'category_id' => $beverageCategory->id,
                    'name' => 'น้ำเก๊กฮวย',
                    'description' => 'น้ำเก๊กฮวยเย็นชื่นใจ หวานกำลังดี',
                    'price' => 20.00,
                    'is_featured' => false,
                    'is_active' => true,
                    'sort_order' => 1
                ],
                [
                    'category_id' => $beverageCategory->id,
                    'name' => 'ชาเย็น',
                    'description' => 'ชาเย็นสูตรโบราณ หอมชา หวานมัน',
                    'price' => 25.00,
                    'image' => 'images/menu/thai-tea.svg',
                    'is_featured' => false,
                    'is_active' => true,
                    'sort_order' => 2
                ]
            ];

            foreach ($beverages as $menu) {
                MenuItem::create($menu);
            }
        }

        // ก๋วยเตี๋ยวเนื้อ
        $beefCategory = $categories->where('slug', 'noodle-beef')->first();
        if ($beefCategory) {
            $beefNoodles = [
                [
                    'category_id' => $beefCategory->id,
                    'name' => 'ก๋วยเตี๋ยวเรือเนื้อธรรมดา',
                    'description' => 'ก๋วยเตี๋ยวเรือเนื้อสไตล์ดั้งเดิม น้ำซุปเข้มข้น',
                    'price' => 50.00,
                    'is_featured' => false,
                    'is_active' => true,
                    'sort_order' => 1
                ],
                [
                    'category_id' => $beefCategory->id,
                    'name' => 'ก๋วยเตี๋ยวเรือเนื้อพิเศษ',
                    'description' => 'ก๋วยเตี๋ยวเรือเนื้อพิเศษ เนื้อเยอะ เครื่องในครบ',
                    'price' => 65.00,
                    'is_featured' => true,
                    'is_active' => true,
                    'sort_order' => 2
                ]
            ];

            foreach ($beefNoodles as $menu) {
                MenuItem::create($menu);
            }
        }

        // ก๋วยเตี๋ยวหมู
        $porkCategory = $categories->where('slug', 'noodle-pork')->first();
        if ($porkCategory) {
            $porkNoodles = [
                [
                    'category_id' => $porkCategory->id,
                    'name' => 'ก๋วยเตี๋ยวเรือหมูธรรมดา',
                    'description' => 'ก๋วยเตี๋ยวเรือหมูสไตล์ดั้งเดิม รสชาติกลมกล่อม',
                    'price' => 45.00,
                    'is_featured' => false,
                    'is_active' => true,
                    'sort_order' => 1
                ],
                [
                    'category_id' => $porkCategory->id,
                    'name' => 'ก๋วยเตี๋ยวเรือหมูสามชั้น',
                    'description' => 'ก๋วยเตี๋ยวเรือหมูสามชั้นหั่นบาง หอมหวน อร่อย',
                    'price' => 60.00,
                    'is_featured' => true,
                    'is_active' => true,
                    'sort_order' => 2
                ]
            ];

            foreach ($porkNoodles as $menu) {
                MenuItem::create($menu);
            }
        }
    }
}
