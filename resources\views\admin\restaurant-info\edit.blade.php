@extends('layouts.admin')

@section('title', 'แก้ไขข้อมูลร้าน - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ route('admin.restaurant-info.index') }}" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่ข้อมูลร้าน
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขข้อมูลร้าน
                </h1>
            </div>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('admin.restaurant-info.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <div class="row g-4">
            <!-- Basic Information -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>ข้อมูลพื้นฐาน
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">ชื่อร้าน <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                   id="name" name="name" value="{{ old('name', $restaurantInfo->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">คำอธิบายร้าน</label>
                            <textarea class="form-control @error('description') is-invalid @enderror"
                                      id="description" name="description" rows="4">{{ old('description', $restaurantInfo->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="tagline" class="form-label">สโลแกน/ข้อความโปรโมท</label>
                            <textarea class="form-control @error('tagline') is-invalid @enderror"
                                      id="tagline" name="tagline" rows="3"
                                      placeholder="เช่น ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย">{{ old('tagline', $restaurantInfo->tagline) }}</textarea>
                            @error('tagline')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">ข้อความนี้จะแสดงในส่วน Hero Section ของหน้าแรก</div>
                        </div>

                        <div class="mb-3">
                            <label for="address" class="form-label">ที่อยู่</label>
                            <textarea class="form-control @error('address') is-invalid @enderror" 
                                      id="address" name="address" rows="3">{{ old('address', $restaurantInfo->address) }}</textarea>
                            @error('address')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-phone me-2"></i>ข้อมูลติดต่อ
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="phone" class="form-label">โทรศัพท์</label>
                            <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                   id="phone" name="phone" value="{{ old('phone', $restaurantInfo->phone) }}">
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="mobile" class="form-label">มือถือ</label>
                            <input type="text" class="form-control @error('mobile') is-invalid @enderror" 
                                   id="mobile" name="mobile" value="{{ old('mobile', $restaurantInfo->mobile) }}">
                            @error('mobile')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">อีเมล</label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                   id="email" name="email" value="{{ old('email', $restaurantInfo->email) }}">
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="website" class="form-label">เว็บไซต์</label>
                            <input type="url" class="form-control @error('website') is-invalid @enderror" 
                                   id="website" name="website" value="{{ old('website', $restaurantInfo->website) }}">
                            @error('website')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Media -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-share-alt me-2"></i>โซเชียลมีเดีย
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="facebook" class="form-label">Facebook</label>
                            <input type="url" class="form-control @error('facebook') is-invalid @enderror"
                                   id="facebook" name="facebook" value="{{ old('facebook', $restaurantInfo->facebook) }}"
                                   placeholder="https://www.facebook.com/profile.php?id=100083370400848&locale=fr_FR">
                            <div class="form-text">ใส่ลิงก์ Facebook ของร้าน (จะแสดงในส่วน Footer ของเว็บไซต์)</div>
                            @error('facebook')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="line" class="form-label">Line ID</label>
                            <input type="text" class="form-control @error('line') is-invalid @enderror"
                                   id="line" name="line" value="{{ old('line', $restaurantInfo->line) }}"
                                   placeholder="@lastnoodle หรือ lastnoodle">
                            <div class="form-text">ใส่ Line ID ของร้าน (สามารถใส่ @ หรือไม่ใส่ก็ได้)</div>
                            @error('line')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="instagram" class="form-label">Instagram</label>
                            <input type="text" class="form-control @error('instagram') is-invalid @enderror"
                                   id="instagram" name="instagram" value="{{ old('instagram', $restaurantInfo->instagram) }}"
                                   placeholder="@lastnoodle_official หรือ lastnoodle_official">
                            <div class="form-text">ใส่ Instagram username ของร้าน (สามารถใส่ @ หรือไม่ใส่ก็ได้)</div>
                            @error('instagram')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Opening Hours -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>เวลาเปิด-ปิด
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="open_time" class="form-label">เวลาเปิด</label>
                                    <input type="time" class="form-control @error('open_time') is-invalid @enderror" 
                                           id="open_time" name="open_time" value="{{ old('open_time', $restaurantInfo->open_time) }}">
                                    @error('open_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="close_time" class="form-label">เวลาปิด</label>
                                    <input type="time" class="form-control @error('close_time') is-invalid @enderror" 
                                           id="close_time" name="close_time" value="{{ old('close_time', $restaurantInfo->close_time) }}">
                                    @error('close_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">วันเปิดทำการ</label>
                            <div class="row">
                                @php
                                    $days = [
                                        'monday' => 'จันทร์',
                                        'tuesday' => 'อังคาร',
                                        'wednesday' => 'พุธ',
                                        'thursday' => 'พฤหัสบดี',
                                        'friday' => 'ศุกร์',
                                        'saturday' => 'เสาร์',
                                        'sunday' => 'อาทิตย์'
                                    ];
                                    $selectedDays = old('open_days', $restaurantInfo->open_days ?? []);
                                @endphp
                                @foreach($days as $key => $day)
                                    <div class="col-md-6 col-lg-12 col-xl-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" 
                                                   name="open_days[]" value="{{ $key }}" 
                                                   id="day_{{ $key }}"
                                                   {{ in_array($key, $selectedDays) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="day_{{ $key }}">
                                                {{ $day }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Images -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2"></i>รูปภาพ
                        </h5>
                        <small class="text-white-50">จัดการรูปภาพต่างๆ ของร้าน</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="logo" class="form-label">
                                        <i class="fas fa-image me-2 text-primary"></i>โลโก้ร้าน
                                        <span class="badge bg-primary ms-2">แสดงใน Navigation</span>
                                    </label>
                                    <input type="file" class="form-control @error('logo') is-invalid @enderror"
                                           id="logo" name="logo" accept="image/*">
                                    @error('logo')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">รูปแนะนำ: ขนาด 200x200px, ไฟล์ JPG/PNG</small>
                                    @if($restaurantInfo->logo)
                                        <div class="mt-3 p-3 bg-light rounded">
                                            <div class="text-center">
                                                <img src="{{ asset('storage/' . $restaurantInfo->logo) }}"
                                                     alt="โลโก้ปัจจุบัน" class="img-thumbnail mb-2" style="max-height: 120px;">
                                                <div class="small text-muted">โลโก้ปัจจุบัน</div>
                                                <div class="small text-success">
                                                    <i class="fas fa-check-circle me-1"></i>กำลังใช้งาน
                                                </div>
                                            </div>
                                        </div>
                                    @else
                                        <div class="mt-3 p-3 bg-light rounded text-center">
                                            <i class="fas fa-image fa-3x text-muted mb-2"></i>
                                            <div class="small text-muted">ยังไม่มีโลโก้</div>
                                            <div class="small text-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>จะแสดงไอคอนเรือแทน
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="cover_image" class="form-label">รูปปกร้าน</label>
                                    <input type="file" class="form-control @error('cover_image') is-invalid @enderror"
                                           id="cover_image" name="cover_image" accept="image/*">
                                    @error('cover_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($restaurantInfo->cover_image)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $restaurantInfo->cover_image) }}"
                                                 alt="รูปปกปัจจุบัน" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">รูปปกปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="background_image" class="form-label">รูปพื้นหลังหน้าหลัก</label>
                                    <input type="file" class="form-control @error('background_image') is-invalid @enderror"
                                           id="background_image" name="background_image" accept="image/*">
                                    @error('background_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($restaurantInfo->background_image)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $restaurantInfo->background_image) }}"
                                                 alt="รูปพื้นหลังปัจจุบัน" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">รูปพื้นหลังปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Map -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map-marker-alt me-2"></i>แผนที่และตำแหน่ง
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">ละติจูด (Latitude)</label>
                                    <input type="number" step="any" class="form-control @error('latitude') is-invalid @enderror" 
                                           id="latitude" name="latitude" value="{{ old('latitude', $restaurantInfo->latitude) }}">
                                    @error('latitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">ลองจิจูด (Longitude)</label>
                                    <input type="number" step="any" class="form-control @error('longitude') is-invalid @enderror" 
                                           id="longitude" name="longitude" value="{{ old('longitude', $restaurantInfo->longitude) }}">
                                    @error('longitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="map_embed" class="form-label">โค้ด Embed แผนที่ (Google Maps)</label>
                            <textarea class="form-control @error('map_embed') is-invalid @enderror" 
                                      id="map_embed" name="map_embed" rows="4" 
                                      placeholder='<iframe src="https://www.google.com/maps/embed?..." width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy"></iframe>'>{{ old('map_embed', $restaurantInfo->map_embed) }}</textarea>
                            @error('map_embed')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">คัดลอกโค้ด iframe จาก Google Maps</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                    <a href="{{ route('admin.restaurant-info.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>ยกเลิก
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>บันทึกข้อมูล
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

@push('styles')
<style>
.card {
    transition: all 0.3s ease;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Back Button Styles */
.btn-back {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.btn-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-back:hover::before {
    left: 100%;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B4513;
    color: #8B4513;
    background: #f8f9fa;
}

.btn-back i {
    transition: transform 0.3s ease;
}

.btn-back:hover i {
    transform: translateX(-3px);
}
</style>
@endpush
@endsection
