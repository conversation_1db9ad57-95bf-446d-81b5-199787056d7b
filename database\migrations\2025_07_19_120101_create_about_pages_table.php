<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('about_pages', function (Blueprint $table) {
            $table->id();
            $table->string('title')->default('เกี่ยวกับเรา');
            $table->text('content')->nullable();
            $table->string('hero_image')->nullable(); // รูปหลักของหน้า
            $table->string('story_image')->nullable(); // รูปประกอบเรื่องราว
            $table->string('team_image')->nullable(); // รูปทีมงาน
            $table->string('gallery_image_1')->nullable(); // รูปแกลเลอรี่ 1
            $table->string('gallery_image_2')->nullable(); // รูปแกลเลอรี่ 2
            $table->string('gallery_image_3')->nullable(); // รูปแกลเลอรี่ 3
            $table->text('our_story')->nullable(); // เรื่องราวของเรา
            $table->text('our_mission')->nullable(); // พันธกิจ
            $table->text('our_vision')->nullable(); // วิสัยทัศน์
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('about_pages');
    }
};
