<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการเข้าสู่ระบบ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4>ทดสอบการเข้าสู่ระบบ</h4>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success">{{ session('success') }}</div>
                        @endif
                        
                        @if(session('error'))
                            <div class="alert alert-danger">{{ session('error') }}</div>
                        @endif
                        
                        @if($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        
                        <form method="POST" action="{{ route('test.login.post') }}">
                            @csrf
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">อีเมล</label>
                                <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">รหัสผ่าน</label>
                                <input type="password" class="form-control" id="password" name="password" value="admin123" required>
                            </div>

                            <!-- Quick Admin Login Buttons -->
                            <div class="mb-3">
                                <div class="row">
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="fillAdminCredentials()">
                                            <i class="fas fa-user-shield"></i> ใส่ข้อมูลแอดมิน
                                        </button>
                                    </div>
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="clearForm()">
                                            <i class="fas fa-eraser"></i> ล้างข้อมูล
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                <label class="form-check-label" for="remember">จดจำการเข้าสู่ระบบ</label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">เข้าสู่ระบบ</button>
                        </form>
                        
                        <hr>
                        
                        <div class="text-center">
                            <h6>ข้อมูลทดสอบ:</h6>
                            <p class="mb-1"><strong>อีเมล:</strong> <EMAIL></p>
                            <p class="mb-1"><strong>รหัสผ่าน:</strong> admin123</p>
                            <p class="mb-1"><strong>บทบาท:</strong> admin</p>
                        </div>
                        
                        @auth
                            <div class="alert alert-success mt-3">
                                <h6>เข้าสู่ระบบสำเร็จ!</h6>
                                <p class="mb-1"><strong>ชื่อ:</strong> {{ Auth::user()->name }}</p>
                                <p class="mb-1"><strong>อีเมล:</strong> {{ Auth::user()->email }}</p>
                                <p class="mb-1"><strong>บทบาท:</strong> {{ Auth::user()->role }}</p>
                                
                                <div class="mt-3">
                                    @if(Auth::user()->isAdmin())
                                        <a href="{{ route('admin.dashboard') }}" class="btn btn-success me-2">ไปยัง Admin Dashboard</a>
                                    @endif
                                    <a href="{{ route('home') }}" class="btn btn-info me-2">ไปยังหน้าหลัก</a>
                                    
                                    <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-danger">ออกจากระบบ</button>
                                    </form>
                                </div>
                            </div>
                        @endauth
                        
                        <div class="mt-3">
                            <h6>ลิงก์ทดสอบ:</h6>
                            <a href="{{ route('login') }}" class="btn btn-outline-primary btn-sm me-2">หน้า Login ปกติ</a>
                            <a href="{{ route('home') }}" class="btn btn-outline-info btn-sm me-2">หน้าหลัก</a>
                            @auth
                                @if(Auth::user()->isAdmin())
                                    <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-success btn-sm">Admin Dashboard</a>
                                @endif
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    function fillAdminCredentials() {
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('password').value = 'admin123';
    }

    function clearForm() {
        document.getElementById('email').value = '';
        document.getElementById('password').value = '';
        document.getElementById('remember').checked = false;
    }
    </script>
</body>
</html>
