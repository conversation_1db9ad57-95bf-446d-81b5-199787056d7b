@extends('layouts.app')

@section('title', 'รายละเอียดหมวดหมู่ - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-eye me-2"></i>รายละเอียดหมวดหมู่: {{ $category->name }}
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('admin.categories.index') }}">หมวดหมู่อาหาร</a>
                            </li>
                            <li class="breadcrumb-item active">{{ $category->name }}</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group">
                    <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>แก้ไข
                    </a>
                    <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Category Details -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>ข้อมูลหมวดหมู่
                    </h5>
                </div>
                <div class="card-body">
                    @if($category->image)
                        <img src="{{ asset('storage/' . $category->image) }}" 
                             alt="{{ $category->name }}" 
                             class="img-fluid rounded mb-3"
                             style="width: 100%; height: 200px; object-fit: cover;">
                    @else
                        <div class="bg-light rounded mb-3 d-flex align-items-center justify-content-center" 
                             style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                    @endif
                    
                    <table class="table table-borderless">
                        <tr>
                            <td class="fw-bold">ID:</td>
                            <td>{{ $category->id }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">ชื่อ:</td>
                            <td>{{ $category->name }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Slug:</td>
                            <td><code>{{ $category->slug }}</code></td>
                        </tr>
                        <tr>
                            <td class="fw-bold">ลำดับ:</td>
                            <td>{{ $category->sort_order }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">สถานะ:</td>
                            <td>
                                @if($category->is_active)
                                    <span class="badge bg-success">เปิดใช้งาน</span>
                                @else
                                    <span class="badge bg-danger">ปิดใช้งาน</span>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td class="fw-bold">สร้างเมื่อ:</td>
                            <td>{{ $category->created_at->format('d/m/Y H:i') }}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">แก้ไขล่าสุด:</td>
                            <td>{{ $category->updated_at->format('d/m/Y H:i') }}</td>
                        </tr>
                    </table>
                    
                    @if($category->description)
                        <div class="mt-3">
                            <h6 class="fw-bold">คำอธิบาย:</h6>
                            <p class="text-muted">{{ $category->description }}</p>
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>สถิติ
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h3 class="text-primary">{{ $category->menuItems->count() }}</h3>
                                <small class="text-muted">เมนูทั้งหมด</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h3 class="text-success">{{ $category->menuItems->where('is_active', true)->count() }}</h3>
                            <small class="text-muted">เมนูที่เปิดใช้งาน</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-12">
                            <h3 class="text-warning">{{ $category->menuItems->where('is_featured', true)->count() }}</h3>
                            <small class="text-muted">เมนูแนะนำ</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Menu Items -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-utensils me-2"></i>เมนูอาหารในหมวดหมู่นี้
                    </h5>
                    <a href="{{ route('admin.menu-items.create') }}?category={{ $category->id }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>เพิ่มเมนูใหม่
                    </a>
                </div>
                <div class="card-body">
                    @if($category->menuItems->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 60px;">รูป</th>
                                        <th>ชื่อเมนู</th>
                                        <th style="width: 100px;">ราคา</th>
                                        <th style="width: 80px;">สถานะ</th>
                                        <th style="width: 100px;">การจัดการ</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($category->menuItems as $item)
                                        <tr>
                                            <td>
                                                @if($item->image)
                                                    <img src="{{ asset('storage/' . $item->image) }}" 
                                                         alt="{{ $item->name }}" 
                                                         class="rounded" 
                                                         style="width: 40px; height: 40px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                <div>
                                                    <strong>{{ $item->name }}</strong>
                                                    @if($item->is_featured)
                                                        <span class="badge bg-warning text-dark ms-1">แนะนำ</span>
                                                    @endif
                                                </div>
                                                @if($item->description)
                                                    <small class="text-muted">{{ Str::limit($item->description, 50) }}</small>
                                                @endif
                                            </td>
                                            <td>
                                                <strong class="text-primary">{{ $item->formatted_price }}</strong>
                                            </td>
                                            <td>
                                                @if($item->is_active)
                                                    <span class="badge bg-success">เปิด</span>
                                                @else
                                                    <span class="badge bg-danger">ปิด</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.menu-items.show', $item) }}" 
                                                       class="btn btn-sm btn-outline-info" 
                                                       title="ดู">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.menu-items.edit', $item) }}" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="แก้ไข">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">ยังไม่มีเมนูในหมวดหมู่นี้</h5>
                            <p class="text-muted">เริ่มต้นเพิ่มเมนูแรกในหมวดหมู่นี้</p>
                            <a href="{{ route('admin.menu-items.create') }}?category={{ $category->id }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>เพิ่มเมนูแรก
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
