<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

try {
    // Check if admin user already exists
    $adminExists = User::where('email', '<EMAIL>')->exists();

    if (!$adminExists) {
        $admin = User::create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin'
        ]);
        
        echo "✅ Admin user created successfully!\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n";
    } else {
        echo "ℹ️  Admin user already exists.\n";
        echo "Email: <EMAIL>\n";
        echo "Password: admin123\n";
    }
    
    // Verify admin user
    $admin = User::where('email', '<EMAIL>')->first();
    if ($admin && $admin->isAdmin()) {
        echo "✅ Admin user verified successfully!\n";
    } else {
        echo "❌ Admin user verification failed!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
