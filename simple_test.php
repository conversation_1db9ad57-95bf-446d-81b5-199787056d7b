<?php

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use Illuminate\Support\Facades\Hash;

echo "=== Simple Test ===\n";

// Check if admin user exists
$admin = User::where('email', '<EMAIL>')->first();

if ($admin) {
    echo "✓ Admin user found\n";
    echo "  ID: {$admin->id}\n";
    echo "  Name: {$admin->name}\n";
    echo "  Email: {$admin->email}\n";
    echo "  Role: {$admin->role}\n";
    
    // Test password
    $passwordTest = Hash::check('admin123', $admin->password);
    echo "  Password test: " . ($passwordTest ? 'PASS' : 'FAIL') . "\n";
    
    if (!$passwordTest) {
        echo "  Updating password...\n";
        $admin->password = Hash::make('admin123');
        $admin->save();
        echo "  Password updated\n";
        
        // Test again
        $passwordTest2 = Hash::check('admin123', $admin->password);
        echo "  Password test 2: " . ($passwordTest2 ? 'PASS' : 'FAIL') . "\n";
    }
    
} else {
    echo "✗ Admin user not found, creating...\n";
    
    $admin = User::create([
        'name' => 'Admin',
        'email' => '<EMAIL>',
        'password' => Hash::make('admin123'),
        'role' => 'admin'
    ]);
    
    echo "✓ Admin user created with ID: {$admin->id}\n";
}

echo "\nTest completed. Try logging in with:\n";
echo "Email: <EMAIL>\n";
echo "Password: admin123\n";
echo "URL: http://127.0.0.1:8000/login\n";
