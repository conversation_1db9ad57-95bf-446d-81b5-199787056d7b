<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MenuItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'category_id',
        'name',
        'description',
        'price',
        'image',
        'is_featured',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationship with category
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    // Scope for active menu items
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    // Scope for featured menu items
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    // Scope for ordered menu items
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    // Get formatted price
    public function getFormattedPriceAttribute()
    {
        return $this->price ? number_format($this->price, 0) . ' บาท' : 'ราคาตามสอบถาม';
    }
}
