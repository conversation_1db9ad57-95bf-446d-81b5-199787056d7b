@extends('layouts.admin')

@section('title', 'จัดการข้อความหน้าแรก - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary btn-back">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับสู่หน้าหลัก
            </a>
        </div>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-home me-2"></i>จัดการข้อความหน้าแรก
                </h1>
                <a href="{{ route('admin.hero-content.edit') }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขข้อความ
                </a>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Hero Content Preview -->
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>ตัวอย่างข้อความหน้าแรก
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Hero Preview -->
                    <div class="hero-preview p-4 rounded" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ $restaurantInfo->background_image ? asset('storage/' . $restaurantInfo->background_image) : asset('images/restaurant/background.jpg') }}'); background-size: cover; background-position: center; color: white; text-align: center;">
                        <h1 class="display-4 fw-bold mb-3">
                            {{ $restaurantInfo->name ?: 'ร้านก๋วยเตี๋ยวเรือเข้าท่า' }}
                        </h1>
                        <p class="lead mb-3">
                            {{ $restaurantInfo->description ?: 'ก๋วยเตี๋ยวเรือต้นตำรับ รสชาติดั้งเดิม ด้วยสูตรลับเฉพาะตัว เครื่องเทศครบครอง' }}
                        </p>
                        <p class="mb-4">
                            {{ $restaurantInfo->tagline ?: 'ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย' }}
                        </p>
                        <div class="hero-buttons">
                            <button class="btn btn-warning btn-lg me-3 px-4 py-2">
                                <i class="fas fa-utensils me-2"></i>ดูเมนูอาหาร
                            </button>
                            <button class="btn btn-outline-light btn-lg px-4 py-2">
                                <i class="fas fa-cog me-2"></i>จัดการระบบ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Content Details -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>ข้อความปัจจุบัน
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="border rounded p-3">
                                <h6 class="text-primary mb-2">
                                    <i class="fas fa-heading me-2"></i>ชื่อร้าน (หัวข้อหลัก)
                                </h6>
                                <p class="mb-0">{{ $restaurantInfo->name ?: 'ยังไม่ได้กำหนด' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3">
                                <h6 class="text-primary mb-2">
                                    <i class="fas fa-align-left me-2"></i>คำอธิบาย (หัวข้อรอง)
                                </h6>
                                <p class="mb-0">{{ $restaurantInfo->description ?: 'ยังไม่ได้กำหนด' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="border rounded p-3">
                                <h6 class="text-primary mb-2">
                                    <i class="fas fa-quote-right me-2"></i>สโลแกน (ข้อความโปรโมท)
                                </h6>
                                <p class="mb-0">{{ $restaurantInfo->tagline ?: 'ยังไม่ได้กำหนด' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>การจัดการอื่นๆ
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ route('admin.restaurant-info.index') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-store me-2"></i>ข้อมูลร้านทั้งหมด
                            </a>
                        </div>
                        {{-- ลิงก์จัดการสไลด์ถูกลบออกแล้ว --}}
                        <div class="col-md-3">
                            <a href="{{ route('home') }}" class="btn btn-outline-success w-100" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>ดูหน้าเว็บไซต์
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>กลับหน้าหลัก
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.hero-preview {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.card {
    transition: all 0.3s ease;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}

.border {
    border-color: #dee2e6 !important;
}

.border:hover {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
}

/* Back Button Styles */
.btn-back {
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: 2px solid #6c757d;
    color: #6c757d;
    background: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.btn-back::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 117, 125, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn-back:hover::before {
    left: 100%;
}

.btn-back:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #8B4513;
    color: #8B4513;
    background: #f8f9fa;
}

.btn-back i {
    transition: transform 0.3s ease;
}

.btn-back:hover i {
    transform: translateX(-3px);
}
</style>
@endpush
@endsection
