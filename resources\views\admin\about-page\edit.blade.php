@extends('layouts.admin')

@section('title', 'แก้ไขหน้าเกี่ยวกับเรา - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขหน้าเกี่ยวกับเรา
                </h1>
                <a href="{{ route('admin.about-page.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>กลับ
                </a>
            </div>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('admin.about-page.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <div class="row g-4">
            <!-- Basic Information -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>เนื้อหาหลัก
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="title" class="form-label">หัวข้อหน้า <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                   id="title" name="title" value="{{ old('title', $aboutPage->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">เนื้อหาหลัก</label>
                            <textarea class="form-control @error('content') is-invalid @enderror" 
                                      id="content" name="content" rows="6">{{ old('content', $aboutPage->content) }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Story Content -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-book me-2"></i>เรื่องราวและวิสัยทัศน์
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="our_story" class="form-label">เรื่องราวของเรา</label>
                            <textarea class="form-control @error('our_story') is-invalid @enderror"
                                      id="our_story" name="our_story" rows="4">{{ old('our_story', $aboutPage->our_story) }}</textarea>
                            @error('our_story')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="main_content" class="form-label">เนื้อหาหลัก</label>
                            <textarea class="form-control @error('main_content') is-invalid @enderror"
                                      id="main_content" name="main_content" rows="6">{{ old('main_content', $aboutPage->main_content) }}</textarea>
                            @error('main_content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="our_mission" class="form-label">พันธกิจ</label>
                                    <textarea class="form-control @error('our_mission') is-invalid @enderror" 
                                              id="our_mission" name="our_mission" rows="4">{{ old('our_mission', $aboutPage->our_mission) }}</textarea>
                                    @error('our_mission')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="our_vision" class="form-label">วิสัยทัศน์</label>
                                    <textarea class="form-control @error('our_vision') is-invalid @enderror" 
                                              id="our_vision" name="our_vision" rows="4">{{ old('our_vision', $aboutPage->our_vision) }}</textarea>
                                    @error('our_vision')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Images -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2"></i>รูปภาพหลัก
                        </h5>
                        <small class="text-white-50">รูปภาพสำคัญที่แสดงในหน้าเกี่ยวกับเรา</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <x-image-picker
                                    name="hero_image"
                                    label="รูป Hero (หน้าหลัก)"
                                    category="about"
                                    :value="$aboutPage->hero_image"
                                    :required="false" />

                                @error('hero_image')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">รูปหลักที่แสดงในหน้าเกี่ยวกับเรา</small>
                            </div>
                            <div class="col-md-4">
                                <x-image-picker
                                    name="story_image"
                                    label="รูปเรื่องราว"
                                    category="about"
                                    :value="$aboutPage->story_image"
                                    :required="false" />

                                @error('story_image')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">รูปประกอบเรื่องราวของร้าน</small>
                            </div>
                            <div class="col-md-4">
                                <x-image-picker
                                    name="team_image"
                                    label="รูปทีมงาน"
                                    category="about"
                                    :value="$aboutPage->team_image"
                                    :required="false" />

                                @error('team_image')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                                <small class="text-muted">รูปทีมงานของร้าน</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <!-- Gallery Images -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-photo-video me-2"></i>แกลเลอรี่
                        </h5>
                        <small class="text-white-50">รูปภาพเพิ่มเติมสำหรับแสดงในแกลเลอรี่</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <x-image-picker
                                    name="gallery_image_1"
                                    label="รูปแกลเลอรี่ 1"
                                    category="about"
                                    :value="$aboutPage->gallery_image_1"
                                    :required="false" />

                                @error('gallery_image_1')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <x-image-picker
                                    name="gallery_image_2"
                                    label="รูปแกลเลอรี่ 2"
                                    category="about"
                                    :value="$aboutPage->gallery_image_2"
                                    :required="false" />

                                @error('gallery_image_2')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-4">
                                <x-image-picker
                                    name="gallery_image_3"
                                    label="รูปแกลเลอรี่ 3"
                                    category="about"
                                    :value="$aboutPage->gallery_image_3"
                                    :required="false" />

                                @error('gallery_image_3')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                    <a href="{{ route('admin.about-page.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>ยกเลิก
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>บันทึกข้อมูล
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

@push('styles')
<style>
.card {
    transition: all 0.3s ease;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}
</style>
@endpush
@endsection
