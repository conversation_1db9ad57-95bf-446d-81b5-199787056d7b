@extends('layouts.admin')

@section('title', 'แก้ไขหน้าติดต่อเรา - ร้านก๋วยเตี๋ยวเรือเข้าท่า')

@section('content')
<div class="container py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 text-primary">
                    <i class="fas fa-edit me-2"></i>แก้ไขหน้าติดต่อเรา
                </h1>
                <a href="{{ route('admin.contact-page.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>กลับ
                </a>
            </div>
        </div>
    </div>

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form action="{{ route('admin.contact-page.update') }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')

        <div class="row g-4">
            <!-- Basic Information -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>ข้อมูลหลัก
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">หัวข้อหน้า <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('title') is-invalid @enderror" 
                                           id="title" name="title" value="{{ old('title', $contactPage->title) }}" required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="hero_image" class="form-label">รูป Hero</label>
                                    <input type="file" class="form-control @error('hero_image') is-invalid @enderror"
                                           id="hero_image" name="hero_image" accept="image/*">
                                    @error('hero_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">รูปหลักที่แสดงในหน้าติดต่อ (มีความสำคัญสูงสุด)</small>
                                    @if($contactPage->hero_image)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $contactPage->hero_image) }}"
                                                 alt="Hero Image ปัจจุบัน" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">รูป Hero ปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="default_background" class="form-label">
                                        <i class="fas fa-image me-2 text-warning"></i>รูปพื้นหลังเริ่มต้น
                                        <span class="badge bg-warning ms-2">สำรอง</span>
                                    </label>
                                    <input type="file" class="form-control @error('default_background') is-invalid @enderror"
                                           id="default_background" name="default_background" accept="image/*">
                                    @error('default_background')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">รูปที่จะแสดงเมื่อไม่มีรูป Hero (ใช้แทนพื้นหลังสีฟ้า)</small>
                                    @if($contactPage->default_background)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $contactPage->default_background) }}"
                                                 alt="รูปพื้นหลังเริ่มต้น" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">รูปพื้นหลังเริ่มต้นปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">คำอธิบายหน้า</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" 
                                      id="description" name="description" rows="3">{{ old('description', $contactPage->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-address-book me-2"></i>ข้อมูลติดต่อ
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="address" class="form-label">ที่อยู่</label>
                                    <textarea class="form-control @error('address') is-invalid @enderror" 
                                              id="address" name="address" rows="3">{{ old('address', $contactPage->address) }}</textarea>
                                    @error('address')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">โทรศัพท์</label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', $contactPage->phone) }}">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="mobile" class="form-label">มือถือ</label>
                                    <input type="text" class="form-control @error('mobile') is-invalid @enderror" 
                                           id="mobile" name="mobile" value="{{ old('mobile', $contactPage->mobile) }}">
                                    @error('mobile')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">อีเมล</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $contactPage->email) }}">
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Social Media -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-share-alt me-2"></i>โซเชียลมีเดีย
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="line_id" class="form-label">Line ID</label>
                                    <input type="text" class="form-control @error('line_id') is-invalid @enderror" 
                                           id="line_id" name="line_id" value="{{ old('line_id', $contactPage->line_id) }}">
                                    @error('line_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="facebook" class="form-label">Facebook URL</label>
                                    <input type="url" class="form-control @error('facebook') is-invalid @enderror" 
                                           id="facebook" name="facebook" value="{{ old('facebook', $contactPage->facebook) }}">
                                    @error('facebook')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="instagram" class="form-label">Instagram URL</label>
                                    <input type="url" class="form-control @error('instagram') is-invalid @enderror" 
                                           id="instagram" name="instagram" value="{{ old('instagram', $contactPage->instagram) }}">
                                    @error('instagram')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Opening Hours -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>เวลาทำการ
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="open_time" class="form-label">เวลาเปิด</label>
                                    <input type="time" class="form-control @error('open_time') is-invalid @enderror" 
                                           id="open_time" name="open_time" value="{{ old('open_time', $contactPage->open_time) }}">
                                    @error('open_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="close_time" class="form-label">เวลาปิด</label>
                                    <input type="time" class="form-control @error('close_time') is-invalid @enderror" 
                                           id="close_time" name="close_time" value="{{ old('close_time', $contactPage->close_time) }}">
                                    @error('close_time')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">วันที่เปิด</label>
                                    <div class="row">
                                        @php
                                            $days = [
                                                'monday' => 'จันทร์',
                                                'tuesday' => 'อังคาร', 
                                                'wednesday' => 'พุธ',
                                                'thursday' => 'พฤหัสบดี',
                                                'friday' => 'ศุกร์',
                                                'saturday' => 'เสาร์',
                                                'sunday' => 'อาทิตย์'
                                            ];
                                            $selectedDays = old('open_days', $contactPage->open_days ?? []);
                                        @endphp
                                        @foreach($days as $key => $day)
                                        <div class="col-6 col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" 
                                                       name="open_days[]" value="{{ $key }}" 
                                                       id="day_{{ $key }}"
                                                       {{ in_array($key, $selectedDays) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="day_{{ $key }}">
                                                    {{ $day }}
                                                </label>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="special_hours" class="form-label">หมายเหตุเวลาพิเศษ</label>
                            <textarea class="form-control @error('special_hours') is-invalid @enderror" 
                                      id="special_hours" name="special_hours" rows="2">{{ old('special_hours', $contactPage->special_hours) }}</textarea>
                            @error('special_hours')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <!-- Location Images -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-images me-2"></i>รูปภาพสถานที่
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="location_image" class="form-label">รูปหน้าร้าน</label>
                                    <input type="file" class="form-control @error('location_image') is-invalid @enderror"
                                           id="location_image" name="location_image" accept="image/*">
                                    @error('location_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($contactPage->location_image)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $contactPage->location_image) }}"
                                                 alt="หน้าร้าน" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">รูปหน้าร้านปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="interior_image" class="form-label">รูปภายในร้าน</label>
                                    <input type="file" class="form-control @error('interior_image') is-invalid @enderror"
                                           id="interior_image" name="interior_image" accept="image/*">
                                    @error('interior_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($contactPage->interior_image)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $contactPage->interior_image) }}"
                                                 alt="ภายในร้าน" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">รูปภายในร้านปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="parking_image" class="form-label">รูปที่จอดรถ</label>
                                    <input type="file" class="form-control @error('parking_image') is-invalid @enderror"
                                           id="parking_image" name="parking_image" accept="image/*">
                                    @error('parking_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    @if($contactPage->parking_image)
                                        <div class="mt-2">
                                            <img src="{{ asset('storage/' . $contactPage->parking_image) }}"
                                                 alt="ที่จอดรถ" class="img-thumbnail" style="max-height: 100px;">
                                            <small class="text-muted d-block">รูปที่จอดรถปัจจุบัน</small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Map & Location -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-map me-2"></i>แผนที่และตำแหน่ง
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">ละติจูด</label>
                                    <input type="number" step="any" class="form-control @error('latitude') is-invalid @enderror"
                                           id="latitude" name="latitude" value="{{ old('latitude', $contactPage->latitude) }}">
                                    @error('latitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">ลองจิจูด</label>
                                    <input type="number" step="any" class="form-control @error('longitude') is-invalid @enderror"
                                           id="longitude" name="longitude" value="{{ old('longitude', $contactPage->longitude) }}">
                                    @error('longitude')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="map_embed" class="form-label">Google Maps Embed Code</label>
                            <textarea class="form-control @error('map_embed') is-invalid @enderror"
                                      id="map_embed" name="map_embed" rows="4"
                                      placeholder="วาง iframe embed code จาก Google Maps ที่นี่">{{ old('map_embed', $contactPage->map_embed) }}</textarea>
                            @error('map_embed')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <small class="text-muted">ไปที่ Google Maps → แชร์ → ฝังแผนที่ → คัดลอกโค้ด HTML</small>
                        </div>

                        <div class="mb-3">
                            <label for="directions" class="form-label">คำแนะนำการเดินทาง</label>
                            <textarea class="form-control @error('directions') is-invalid @enderror"
                                      id="directions" name="directions" rows="3">{{ old('directions', $contactPage->directions) }}</textarea>
                            @error('directions')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>ข้อมูลเพิ่มเติม
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="parking_info" class="form-label">ข้อมูลที่จอดรถ</label>
                                    <textarea class="form-control @error('parking_info') is-invalid @enderror"
                                              id="parking_info" name="parking_info" rows="3">{{ old('parking_info', $contactPage->parking_info) }}</textarea>
                                    @error('parking_info')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="public_transport" class="form-label">ขนส่งสาธารณะ</label>
                                    <textarea class="form-control @error('public_transport') is-invalid @enderror"
                                              id="public_transport" name="public_transport" rows="3">{{ old('public_transport', $contactPage->public_transport) }}</textarea>
                                    @error('public_transport')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="additional_info" class="form-label">ข้อมูลเพิ่มเติม</label>
                                    <textarea class="form-control @error('additional_info') is-invalid @enderror"
                                              id="additional_info" name="additional_info" rows="3">{{ old('additional_info', $contactPage->additional_info) }}</textarea>
                                    @error('additional_info')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="d-flex justify-content-end gap-2">
                    <a href="{{ route('admin.contact-page.index') }}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>ยกเลิก
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>บันทึกข้อมูล
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

@push('styles')
<style>
.card {
    transition: all 0.3s ease;
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
    border-bottom: none !important;
}
</style>
@endpush
@endsection
