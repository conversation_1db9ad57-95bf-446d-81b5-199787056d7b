@extends('layouts.app')

@section('title', ($aboutPage->title ?? 'เกี่ยวกับเรา') . ' - ' . ($restaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า'))

@push('styles')
<style>
    .hero-section {
        min-height: 45vh;
        position: relative;
        overflow: hidden;
    }

    .hero-slide {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        background-attachment: fixed;
        filter: brightness(0.8) contrast(1.1) saturate(1.2);
    }

    .hero-slide::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1;
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .hero-section .container {
        position: relative;
        z-index: 2;
        height: 45vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .text-shadow {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    /* Lightweight loading animations */
    .content-fade-in {
        opacity: 0;
        animation: contentFadeIn 0.5s ease-out forwards;
    }

    @keyframes contentFadeIn {
        to { opacity: 1; }
    }

    .section-slide-up {
        opacity: 0;
        transform: translateY(20px);
        animation: sectionSlideUp 0.6s ease-out forwards;
    }

    @keyframes sectionSlideUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Enhanced Button Styles */
    .btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
        transition: all 0.3s ease;
    }

    .btn-outline-light:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .hero-section {
            min-height: 50vh;
        }

        .hero-section .container {
            height: 50vh;
        }

        .display-2 {
            font-size: 2.5rem;
        }

        .fs-3 {
            font-size: 1.2rem !important;
        }
    }

    .section-divider {
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        margin: 3rem 0;
        border-radius: 2px;
    }

    .story-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
        transition: transform 0.3s ease;
    }

    .story-card:hover {
        transform: translateY(-5px);
    }

    .mission-vision-card {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 2rem;
        height: 100%;
        border-left: 5px solid var(--primary-color);
    }

    .gallery-item {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .gallery-item:hover {
        transform: scale(1.05);
    }



    .contact-info-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        text-align: center;
        height: 100%;
    }

    .contact-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 1.5rem;
    }

    @media (max-width: 768px) {
        .hero-section {
            min-height: 50vh;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
        }

        .hero-content h1 {
            font-size: 2.5rem;
        }

        .hero-content .lead {
            font-size: 1.1rem;
        }

        .mission-vision-card {
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .contact-info-card {
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .story-card {
            margin-bottom: 2rem;
        }

        .gallery-item {
            margin-bottom: 1rem;
        }

        .stat-item {
            padding: 1rem;
        }
    }

    @media (max-width: 576px) {
        .hero-content h1 {
            font-size: 2rem;
        }

        .display-6 {
            font-size: 1.75rem;
        }

        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            font-size: 1.25rem;
        }

        .section-divider {
            margin: 2rem 0;
        }
    }
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-slide" style="background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(139, 69, 19, 0.7)), url('{{ $aboutPage->hero_image ? asset("storage/" . $aboutPage->hero_image) : asset("images/restaurant/background.jpg") }}'); background-size: cover; background-position: center;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-10 mx-auto text-center">
                    <div class="hero-content text-white content-fade-in">
                        <h1 class="display-2 fw-bold mb-4 text-white" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.7);">
                            <i class="fas fa-heart me-3 text-warning"></i>
                            {{ $aboutPage->title ?? 'เกี่ยวกับเรา' }}
                        </h1>
                        <p class="lead mb-5 fs-3 mx-auto text-white" style="max-width: 800px; line-height: 1.6; text-shadow: 1px 1px 3px rgba(0,0,0,0.6);">
                            {{ $restaurantInfo->tagline ?? 'ลิ้มลองรสชาติความอร่อยระดับตำนาน ที่ยังคงไว้ซึ่งเอกลักษณ์ไทย' }}
                        </p>

                        <!-- Decorative Line -->
                        <div class="d-flex justify-content-center align-items-center mb-5">
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                            <div class="mx-3">
                                <i class="fas fa-heart text-warning fs-4"></i>
                            </div>
                            <div class="bg-warning opacity-75" style="height: 2px; width: 60px; border-radius: 2px;"></div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Decorative elements removed for better performance --}}
</section>

<!-- Main Content Section -->
@if($aboutPage->main_content)
<section class="py-5 section-slide-up">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="text-center mb-5">
                    <h2 class="display-6 fw-bold text-primary mb-4">
                        {{ $restaurantInfo->name ?? 'ร้านก๋วยเตี๋ยวเรือเข้าท่า' }}
                    </h2>
                    <div class="lead text-muted">
                        {!! nl2br(e($aboutPage->main_content)) !!}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<div class="section-divider"></div>

<!-- Our Story Section -->
@if($aboutPage->our_story)
<section id="our-story" class="py-5 bg-light section-slide-up">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="story-card">
                    @if($aboutPage->story_image)
                        <img src="{{ asset('storage/' . $aboutPage->story_image) }}"
                             alt="เรื่องราวของเรา"
                             class="img-fluid img-loading">
                    @else
                        <div class="bg-primary d-flex align-items-center justify-content-center" style="height: 400px;">
                            <i class="fas fa-image fa-4x text-white opacity-50"></i>
                        </div>
                    @endif
                </div>
            </div>
            <div class="col-lg-6">
                <h2 class="display-6 fw-bold text-primary mb-4">เรื่องราวของเรา</h2>
                <div class="text-muted">
                    {!! nl2br(e($aboutPage->our_story)) !!}
                </div>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Mission & Vision Section -->
@if($aboutPage->our_mission || $aboutPage->our_vision)
<section class="py-5">
    <div class="container">
        <div class="row">
            @if($aboutPage->our_mission)
            <div class="col-lg-6 mb-4">
                <div class="mission-vision-card">
                    <div class="d-flex align-items-center mb-3">
                        <div class="contact-icon me-3">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <h3 class="fw-bold text-primary mb-0">พันธกิจ</h3>
                    </div>
                    <p class="text-muted mb-0">
                        {{ $aboutPage->our_mission }}
                    </p>
                </div>
            </div>
            @endif

            @if($aboutPage->our_vision)
            <div class="col-lg-6 mb-4">
                <div class="mission-vision-card">
                    <div class="d-flex align-items-center mb-3">
                        <div class="contact-icon me-3">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3 class="fw-bold text-primary mb-0">วิสัยทัศน์</h3>
                    </div>
                    <p class="text-muted mb-0">
                        {{ $aboutPage->our_vision }}
                    </p>
                </div>
            </div>
            @endif
        </div>
    </div>
</section>
@endif

<!-- Team Section -->
@if($aboutPage->team_image)
<section class="py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 order-lg-2 mb-4 mb-lg-0">
                <div class="story-card">
                    <img src="{{ asset('storage/' . $aboutPage->team_image) }}" 
                         alt="ทีมงานของเรา" 
                         class="img-fluid">
                </div>
            </div>
            <div class="col-lg-6 order-lg-1">
                <h2 class="display-6 fw-bold text-primary mb-4">ทีมงานของเรา</h2>
                <p class="text-muted">
                    ทีมงานมืออาชีพที่มีประสบการณ์ในการทำก๋วยเตี๋ยวเรือมายาวนาน 
                    ด้วยความใส่ใจในทุกรายละเอียด เพื่อมอบความอร่อยที่ดีที่สุดให้กับลูกค้าทุกท่าน
                </p>
            </div>
        </div>
    </div>
</section>
@endif

<!-- Gallery Section -->
@if($aboutPage->gallery_image_1 || $aboutPage->gallery_image_2 || $aboutPage->gallery_image_3)
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-6 fw-bold text-primary">บรรยากาศร้าน</h2>
            <p class="lead text-muted">ชมภาพบรรยากาศและความน่าอร่อยของเมนูต่างๆ</p>
        </div>
        
        <div class="row g-4">
            @if($aboutPage->gallery_image_1)
            <div class="col-lg-4 col-md-6">
                <div class="gallery-item">
                    <img src="{{ asset('storage/' . $aboutPage->gallery_image_1) }}"
                         alt="บรรยากาศร้าน 1"
                         class="img-fluid gallery-image">
                </div>
            </div>
            @endif

            @if($aboutPage->gallery_image_2)
            <div class="col-lg-4 col-md-6">
                <div class="gallery-item">
                    <img src="{{ asset('storage/' . $aboutPage->gallery_image_2) }}"
                         alt="บรรยากาศร้าน 2"
                         class="img-fluid gallery-image">
                </div>
            </div>
            @endif

            @if($aboutPage->gallery_image_3)
            <div class="col-lg-4 col-md-6">
                <div class="gallery-item">
                    <img src="{{ asset('storage/' . $aboutPage->gallery_image_3) }}"
                         alt="บรรยากาศร้าน 3"
                         class="img-fluid gallery-image">
                </div>
            </div>
            @endif
        </div>
    </div>
</section>
@endif



<!-- Contact Information -->
<section id="contact" class="py-5 bg-light section-slide-up">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-6 fw-bold text-primary">ข้อมูลติดต่อ</h2>
            <p class="lead text-muted">มาเยือนเราและลิ้มลองรสชาติก๋วยเตี๋ยวเรือแท้</p>
        </div>

        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="contact-info-card">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h5 class="fw-bold text-primary">ที่อยู่</h5>
                    <p class="text-muted">
                        {{ $restaurantInfo->address ?? 'ถนนเพชรเจริญ, Phetchabun, Thailand, Phetchabun' }}
                    </p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="contact-info-card">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h5 class="fw-bold text-primary">โทรศัพท์</h5>
                    <p class="text-muted">
                        {{ $restaurantInfo->phone ?? '02-123-4567' }}
                    </p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="contact-info-card">
                    <div class="contact-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h5 class="fw-bold text-primary">มือถือ</h5>
                    <p class="text-muted">
                        {{ $restaurantInfo->mobile ?? '************' }}
                    </p>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="contact-info-card">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h5 class="fw-bold text-primary">อีเมล</h5>
                    <p class="text-muted">
                        {{ $restaurantInfo->email ?? '<EMAIL>' }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

{{-- Image modal removed to improve performance --}}

@endsection

@push('styles')
<style>
.gallery-image:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.gallery-item {
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.gallery-item:hover {
    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
    transform: translateY(-5px);
}

#imageModal .modal-content {
    background: transparent !important;
}

#imageModal .modal-body {
    background: transparent;
}

#imageModal .btn-close-white {
    background: rgba(255,255,255,0.8);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    opacity: 1;
}

#imageModal .btn-close-white:hover {
    background: rgba(255,255,255,1);
}
</style>
@endpush

{{-- JavaScript removed to improve page loading speed --}}
